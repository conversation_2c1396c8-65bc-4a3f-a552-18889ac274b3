<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة عروض الأسعار</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            direction: rtl;
        }
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 400px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input:focus {
            outline: none;
            border-color: #667eea;
        }
        button {
            width: 100%;
            background: #667eea;
            color: white;
            padding: 15px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background: #5a6fd8;
        }
        .message {
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            text-align: center;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .links {
            text-align: center;
            margin-top: 20px;
        }
        .links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 10px;
        }
        .default-info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            border: 1px solid #b3d9ff;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1>🔐 تسجيل الدخول</h1>
        
        <?php
        session_start();
        $message = '';
        $messageType = '';
        
        // التحقق من تسجيل الدخول المسبق
        if (isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true) {
            header('Location: simple_dashboard.php');
            exit;
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $username = trim($_POST['username'] ?? '');
            $password = $_POST['password'] ?? '';
            
            if (empty($username) || empty($password)) {
                $message = 'يرجى إدخال اسم المستخدم وكلمة المرور';
                $messageType = 'error';
            } else {
                try {
                    // التحقق من وجود ملف التكوين
                    if (!file_exists('config/database.php')) {
                        $message = 'يجب إعداد قاعدة البيانات أولاً';
                        $messageType = 'error';
                    } else {
                        require_once 'config/database.php';
                        $db = new Database();
                        $pdo = $db->connect();
                        
                        $stmt = $pdo->prepare("SELECT id, username, email, password, full_name, role FROM users WHERE (username = ? OR email = ?) AND is_active = 1");
                        $stmt->execute([$username, $username]);
                        $user = $stmt->fetch(PDO::FETCH_ASSOC);
                        
                        if ($user && password_verify($password, $user['password'])) {
                            // تسجيل الدخول بنجاح
                            $_SESSION['user_id'] = $user['id'];
                            $_SESSION['username'] = $user['username'];
                            $_SESSION['full_name'] = $user['full_name'];
                            $_SESSION['role'] = $user['role'];
                            $_SESSION['email'] = $user['email'];
                            $_SESSION['logged_in'] = true;
                            
                            header('Location: simple_dashboard.php');
                            exit;
                        } else {
                            $message = 'اسم المستخدم أو كلمة المرور غير صحيحة';
                            $messageType = 'error';
                        }
                    }
                } catch (Exception $e) {
                    $message = 'خطأ في الاتصال بقاعدة البيانات';
                    $messageType = 'error';
                }
            }
        }
        ?>
        
        <div class="default-info">
            <strong>بيانات الدخول الافتراضية:</strong><br>
            اسم المستخدم: <strong>admin</strong><br>
            كلمة المرور: <strong>admin123</strong>
        </div>
        
        <?php if ($message): ?>
            <div class="message <?php echo $messageType; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <form method="POST">
            <div class="form-group">
                <label>اسم المستخدم أو البريد الإلكتروني:</label>
                <input type="text" name="username" value="<?php echo htmlspecialchars($_POST['username'] ?? 'admin'); ?>" required>
            </div>
            
            <div class="form-group">
                <label>كلمة المرور:</label>
                <input type="password" name="password" required>
            </div>
            
            <button type="submit">🚀 دخول</button>
        </form>
        
        <div class="links">
            <a href="start.html">🏠 الصفحة الرئيسية</a>
            <a href="simple_setup.php">⚙️ إعداد النظام</a>
        </div>
    </div>
</body>
</html>
