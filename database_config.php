<?php
/**
 * إعدادات قاعدة البيانات
 * نظام إدارة عروض الأسعار الاحترافي
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'quote_management_system';
    private $username = 'root';
    private $password = '';
    private $charset = 'utf8mb4';
    private $conn;

    public function connect() {
        $this->conn = null;
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            $this->conn = new PDO($dsn, $this->username, $this->password, $options);
        } catch(PDOException $e) {
            error_log("Connection Error: " . $e->getMessage());
            throw new Exception("فشل الاتصال بقاعدة البيانات");
        }
        return $this->conn;
    }

    public function disconnect() {
        $this->conn = null;
    }

    public function getConnection() {
        if ($this->conn === null) {
            return $this->connect();
        }
        return $this->conn;
    }

    public function query($sql, $params = []) {
        try {
            $stmt = $this->getConnection()->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch(PDOException $e) {
            error_log("Query Error: " . $e->getMessage());
            throw new Exception("خطأ في تنفيذ الاستعلام");
        }
    }

    public function fetchOne($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }

    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }

    public function insert($sql, $params = []) {
        $this->query($sql, $params);
        return $this->getConnection()->lastInsertId();
    }

    public function update($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }

    public function delete($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }

    public function beginTransaction() {
        return $this->getConnection()->beginTransaction();
    }

    public function commit() {
        return $this->getConnection()->commit();
    }

    public function rollback() {
        return $this->getConnection()->rollback();
    }
}

class DB {
    private static $instance = null;

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new Database();
        }
        return self::$instance;
    }

    public static function connection() {
        return self::getInstance()->getConnection();
    }

    public static function query($sql, $params = []) {
        return self::getInstance()->query($sql, $params);
    }

    public static function fetchOne($sql, $params = []) {
        return self::getInstance()->fetchOne($sql, $params);
    }

    public static function fetchAll($sql, $params = []) {
        return self::getInstance()->fetchAll($sql, $params);
    }

    public static function insert($sql, $params = []) {
        return self::getInstance()->insert($sql, $params);
    }

    public static function update($sql, $params = []) {
        return self::getInstance()->update($sql, $params);
    }

    public static function delete($sql, $params = []) {
        return self::getInstance()->delete($sql, $params);
    }
}
