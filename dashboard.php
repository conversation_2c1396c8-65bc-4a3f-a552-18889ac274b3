<?php
/**
 * لوحة التحكم الرئيسية
 * نظام إدارة عروض الأسعار الاحترافي
 */

// التحقق من وجود ملف التكوين أولاً
if (!file_exists('config/database.php')) {
    header('Location: simple_setup.php');
    exit;
}

try {
    require_once 'classes/Auth.php';
    require_once 'classes/Quote.php';
    require_once 'classes/Customer.php';

    $auth = new Auth();
    $auth->requireLogin();

    $user = $auth->getCurrentUser();
    $quote = new Quote();
    $customer = new Customer();
} catch (Exception $e) {
    // في حالة فشل تحميل الكلاسات، إعادة توجيه للإعداد
    header('Location: simple_setup.php?error=classes_failed');
    exit;
}

// إحصائيات سريعة
$stats = [
    'total_quotes' => count($quote->getAll()),
    'draft_quotes' => count($quote->getAll(['status' => 'draft'])),
    'sent_quotes' => count($quote->getAll(['status' => 'sent'])),
    'approved_quotes' => count($quote->getAll(['status' => 'approved'])),
    'total_customers' => $customer->getCount(),
    'this_month_quotes' => count($quote->getAll([
        'date_from' => date('Y-m-01'),
        'date_to' => date('Y-m-t')
    ]))
];

// أحدث العروض
$recent_quotes = $quote->getAll(['limit' => 5]);

// أحدث العملاء
$recent_customers = $customer->getAll(['limit' => 5]);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة عروض الأسعار</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }
        
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background-color: #f8f9fa;
        }
        
        .sidebar {
            background: var(--primary-color);
            min-height: 100vh;
            width: 250px;
            position: fixed;
            right: 0;
            top: 0;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .sidebar.collapsed {
            width: 70px;
        }
        
        .sidebar-header {
            padding: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }
        
        .sidebar-header h4 {
            color: white;
            margin: 0;
            font-weight: 600;
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .sidebar-menu li {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 1rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .sidebar-menu i {
            width: 20px;
            margin-left: 10px;
            text-align: center;
        }
        
        .main-content {
            margin-right: 250px;
            transition: all 0.3s ease;
        }
        
        .main-content.expanded {
            margin-right: 70px;
        }
        
        .top-navbar {
            background: white;
            padding: 1rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1rem;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dark-color);
        }
        
        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .card-header {
            background: var(--primary-color);
            color: white;
            border-radius: 10px 10px 0 0 !important;
            font-weight: 600;
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
            color: var(--dark-color);
        }
        
        .badge {
            font-size: 0.75rem;
        }
        
        .btn-toggle-sidebar {
            background: none;
            border: none;
            color: var(--dark-color);
            font-size: 1.2rem;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                transform: translateX(100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h4>نظام العروض</h4>
        </div>
        <ul class="sidebar-menu">
            <li><a href="dashboard.php" class="active"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
            <li><a href="quotes.php"><i class="fas fa-file-invoice-dollar"></i> عروض الأسعار</a></li>
            <li><a href="customers.php"><i class="fas fa-users"></i> العملاء</a></li>
            <li><a href="products.php"><i class="fas fa-box"></i> المنتجات</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar"></i> التقارير</a></li>
            <?php if ($user['role'] === 'admin'): ?>
            <li><a href="users.php"><i class="fas fa-user-cog"></i> المستخدمين</a></li>
            <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
            <?php endif; ?>
            <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar">
            <div class="d-flex align-items-center">
                <button class="btn-toggle-sidebar" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <h5 class="mb-0 ms-3">لوحة التحكم</h5>
            </div>
            <div class="d-flex align-items-center">
                <span class="me-3">مرحباً، <?php echo htmlspecialchars($user['full_name']); ?></span>
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>الملف الشخصي</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="container-fluid p-4">
            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: var(--primary-color);">
                            <i class="fas fa-file-invoice-dollar"></i>
                        </div>
                        <div class="stats-number"><?php echo $stats['total_quotes']; ?></div>
                        <div class="stats-label">إجمالي العروض</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: var(--warning-color);">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stats-number"><?php echo $stats['draft_quotes']; ?></div>
                        <div class="stats-label">عروض مسودة</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: var(--success-color);">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stats-number"><?php echo $stats['approved_quotes']; ?></div>
                        <div class="stats-label">عروض موافق عليها</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="stats-card">
                        <div class="stats-icon" style="background: var(--info-color);">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stats-number"><?php echo $stats['total_customers']; ?></div>
                        <div class="stats-label">إجمالي العملاء</div>
                    </div>
                </div>
            </div>

            <!-- Recent Data -->
            <div class="row">
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-file-invoice-dollar me-2"></i>أحدث عروض الأسعار
                        </div>
                        <div class="card-body">
                            <?php if (empty($recent_quotes)): ?>
                                <p class="text-muted text-center">لا توجد عروض أسعار حتى الآن</p>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>رقم العرض</th>
                                                <th>العميل</th>
                                                <th>التاريخ</th>
                                                <th>المبلغ</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recent_quotes as $q): ?>
                                            <tr>
                                                <td><a href="quote_view.php?id=<?php echo $q['id']; ?>" class="text-decoration-none"><?php echo htmlspecialchars($q['quote_number']); ?></a></td>
                                                <td><?php echo htmlspecialchars($q['customer_name']); ?></td>
                                                <td><?php echo date('Y/m/d', strtotime($q['quote_date'])); ?></td>
                                                <td><?php echo number_format($q['total_amount'], 2); ?> ر.س</td>
                                                <td>
                                                    <?php
                                                    $statusColors = [
                                                        'draft' => 'secondary',
                                                        'sent' => 'primary',
                                                        'approved' => 'success',
                                                        'rejected' => 'danger',
                                                        'expired' => 'warning'
                                                    ];
                                                    $statusLabels = [
                                                        'draft' => 'مسودة',
                                                        'sent' => 'مرسل',
                                                        'approved' => 'موافق عليه',
                                                        'rejected' => 'مرفوض',
                                                        'expired' => 'منتهي الصلاحية'
                                                    ];
                                                    ?>
                                                    <span class="badge bg-<?php echo $statusColors[$q['status']]; ?>">
                                                        <?php echo $statusLabels[$q['status']]; ?>
                                                    </span>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-users me-2"></i>أحدث العملاء
                        </div>
                        <div class="card-body">
                            <?php if (empty($recent_customers)): ?>
                                <p class="text-muted text-center">لا يوجد عملاء حتى الآن</p>
                            <?php else: ?>
                                <div class="list-group list-group-flush">
                                    <?php foreach ($recent_customers as $c): ?>
                                    <div class="list-group-item border-0 px-0">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1"><?php echo htmlspecialchars($c['name']); ?></h6>
                                                <small class="text-muted"><?php echo htmlspecialchars($c['company_name'] ?? $c['phone']); ?></small>
                                            </div>
                                            <small class="text-muted"><?php echo date('Y/m/d', strtotime($c['created_at'])); ?></small>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('main-content');
            
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
        }
        
        // إخفاء/إظهار الشريط الجانبي على الشاشات الصغيرة
        function toggleMobileSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }
        
        // إغلاق الشريط الجانبي عند النقر خارجه على الشاشات الصغيرة
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const toggleBtn = document.querySelector('.btn-toggle-sidebar');

            if (window.innerWidth <= 768 &&
                !sidebar.contains(event.target) &&
                !toggleBtn.contains(event.target)) {
                sidebar.classList.remove('show');
            }
        });

        // تحديث الإحصائيات كل 30 ثانية
        setInterval(function() {
            // يمكن إضافة AJAX لتحديث الإحصائيات
        }, 30000);
    </script>
</body>
</html>
