<?php
/**
 * صفحة إدارة العملاء
 * نظام إدارة عروض الأسعار الاحترافي
 */

require_once 'classes/Auth.php';
require_once 'classes/Customer.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('view_customers');

$user = $auth->getCurrentUser();
$customer = new Customer();

// معالجة العمليات
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'create':
            if ($auth->hasPermission('create_customers')) {
                $data = [
                    'name' => $_POST['name'],
                    'phone' => $_POST['phone'],
                    'email' => $_POST['email'],
                    'address' => $_POST['address'],
                    'company_name' => $_POST['company_name'],
                    'tax_number' => $_POST['tax_number'],
                    'notes' => $_POST['notes'],
                    'created_by' => $user['id']
                ];
                
                $errors = $customer->validate($data);
                if (empty($errors)) {
                    $result = $customer->create($data);
                    $message = $result['message'];
                    $messageType = $result['success'] ? 'success' : 'danger';
                } else {
                    $message = implode('<br>', $errors);
                    $messageType = 'danger';
                }
            }
            break;
            
        case 'update':
            if ($auth->hasPermission('edit_customers')) {
                $id = $_POST['customer_id'];
                $data = [
                    'name' => $_POST['name'],
                    'phone' => $_POST['phone'],
                    'email' => $_POST['email'],
                    'address' => $_POST['address'],
                    'company_name' => $_POST['company_name'],
                    'tax_number' => $_POST['tax_number'],
                    'notes' => $_POST['notes']
                ];
                
                $errors = $customer->validate($data, true);
                if (empty($errors)) {
                    $result = $customer->update($id, $data);
                    $message = $result['message'];
                    $messageType = $result['success'] ? 'success' : 'danger';
                } else {
                    $message = implode('<br>', $errors);
                    $messageType = 'danger';
                }
            }
            break;
            
        case 'delete':
            if ($auth->hasPermission('delete_customers')) {
                $id = $_POST['customer_id'];
                $result = $customer->delete($id);
                $message = $result['message'];
                $messageType = $result['success'] ? 'success' : 'danger';
            }
            break;
    }
}

// فلاتر البحث
$filters = [
    'search' => $_GET['search'] ?? '',
    'limit' => 20,
    'offset' => ($_GET['page'] ?? 1 - 1) * 20
];

$customers = $customer->getAll($filters);
$totalCustomers = $customer->getCount($filters);
$totalPages = ceil($totalCustomers / $filters['limit']);
$currentPage = $_GET['page'] ?? 1;
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء - نظام إدارة عروض الأسعار</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }
        
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background-color: #f8f9fa;
        }
        
        .sidebar {
            background: var(--primary-color);
            min-height: 100vh;
            width: 250px;
            position: fixed;
            right: 0;
            top: 0;
            z-index: 1000;
        }
        
        .sidebar-header {
            padding: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }
        
        .sidebar-header h4 {
            color: white;
            margin: 0;
            font-weight: 600;
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .sidebar-menu li {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 1rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .sidebar-menu i {
            width: 20px;
            margin-left: 10px;
            text-align: center;
        }
        
        .main-content {
            margin-right: 250px;
        }
        
        .top-navbar {
            background: white;
            padding: 1rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .card-header {
            background: var(--primary-color);
            color: white;
            border-radius: 10px 10px 0 0 !important;
            font-weight: 600;
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
            color: var(--dark-color);
        }
        
        .btn-action {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            margin: 0 0.125rem;
        }
        
        .search-box {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4>نظام العروض</h4>
        </div>
        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
            <li><a href="quotes.php"><i class="fas fa-file-invoice-dollar"></i> عروض الأسعار</a></li>
            <li><a href="customers.php" class="active"><i class="fas fa-users"></i> العملاء</a></li>
            <li><a href="products.php"><i class="fas fa-box"></i> المنتجات</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar"></i> التقارير</a></li>
            <?php if ($user['role'] === 'admin'): ?>
            <li><a href="users.php"><i class="fas fa-user-cog"></i> المستخدمين</a></li>
            <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
            <?php endif; ?>
            <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar">
            <h5 class="mb-0">إدارة العملاء</h5>
            <div class="d-flex align-items-center">
                <span class="me-3">مرحباً، <?php echo htmlspecialchars($user['full_name']); ?></span>
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>الملف الشخصي</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="container-fluid p-4">
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Search and Add -->
            <div class="search-box">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <form method="GET" class="d-flex">
                            <input type="text" class="form-control me-2" name="search" 
                                   placeholder="البحث عن عميل..." value="<?php echo htmlspecialchars($filters['search']); ?>">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>
                    <div class="col-md-6 text-end">
                        <?php if ($auth->hasPermission('create_customers')): ?>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                            <i class="fas fa-plus me-2"></i>إضافة عميل جديد
                        </button>
                        <?php endif; ?>
                        <button type="button" class="btn btn-success" onclick="exportCustomers()">
                            <i class="fas fa-file-excel me-2"></i>تصدير Excel
                        </button>
                    </div>
                </div>
            </div>

            <!-- Customers Table -->
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-users me-2"></i>قائمة العملاء (<?php echo $totalCustomers; ?> عميل)
                </div>
                <div class="card-body">
                    <?php if (empty($customers)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا يوجد عملاء</h5>
                            <p class="text-muted">ابدأ بإضافة عميل جديد</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>الشركة</th>
                                        <th>الهاتف</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>تاريخ الإضافة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($customers as $c): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($c['name']); ?></strong>
                                        </td>
                                        <td><?php echo htmlspecialchars($c['company_name'] ?? '-'); ?></td>
                                        <td>
                                            <?php if ($c['phone']): ?>
                                                <a href="tel:<?php echo $c['phone']; ?>" class="text-decoration-none">
                                                    <?php echo htmlspecialchars($c['phone']); ?>
                                                </a>
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($c['email']): ?>
                                                <a href="mailto:<?php echo $c['email']; ?>" class="text-decoration-none">
                                                    <?php echo htmlspecialchars($c['email']); ?>
                                                </a>
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('Y/m/d', strtotime($c['created_at'])); ?></td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-outline-info btn-action" 
                                                    onclick="viewCustomer(<?php echo $c['id']; ?>)">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <?php if ($auth->hasPermission('edit_customers')): ?>
                                            <button type="button" class="btn btn-sm btn-outline-warning btn-action" 
                                                    onclick="editCustomer(<?php echo $c['id']; ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <?php endif; ?>
                                            <button type="button" class="btn btn-sm btn-outline-primary btn-action" 
                                                    onclick="createQuote(<?php echo $c['id']; ?>)">
                                                <i class="fas fa-file-invoice-dollar"></i>
                                            </button>
                                            <?php if ($auth->hasPermission('delete_customers')): ?>
                                            <button type="button" class="btn btn-sm btn-outline-danger btn-action" 
                                                    onclick="deleteCustomer(<?php echo $c['id']; ?>, '<?php echo htmlspecialchars($c['name']); ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($totalPages > 1): ?>
                        <nav aria-label="صفحات العملاء">
                            <ul class="pagination justify-content-center">
                                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                <li class="page-item <?php echo $i == $currentPage ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($filters['search']); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                                <?php endfor; ?>
                            </ul>
                        </nav>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function viewCustomer(id) {
            window.location.href = 'customer_view.php?id=' + id;
        }
        
        function editCustomer(id) {
            window.location.href = 'customer_edit.php?id=' + id;
        }
        
        function createQuote(customerId) {
            window.location.href = 'quote_create.php?customer_id=' + customerId;
        }
        
        function deleteCustomer(id, name) {
            if (confirm('هل أنت متأكد من حذف العميل "' + name + '"؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="customer_id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        function exportCustomers() {
            window.location.href = 'customer_export.php';
        }
    </script>
</body>
</html>
