<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <link rel="icon" href="logo.png">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض أسعار</title>
    <style>
        /* أنماط CSS هنا */
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            background-color: #f4f4f4;
        }
        .container {
            width: 90%;
            max-width: 1200px;
            margin: 20px auto;
            background: #fff;
            border-radius: 10px;
            overflow: hidden;
        }
        .header {
            background: #990000;
            color: #fff;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header .logo {
            display: flex;
            align-items: center;
        }
        .header .logo img {
            height: 40px;
            margin-right: 10px;
        }
        .header .logo h1 {
            margin: 0;
            font-size: 14px;
        }
        .header .details {
            text-align: right;
            font-size: 14px;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .table th,
        .table td {
            border: 1px solid #ccc;
            padding: 10px;
            text-align: center;
            font-size: 14px;
        }
        .table th {
            background: #990000;
            color: #fff;
        }
        .notes {
            padding: 20px;
            font-size: 14px;
            line-height: 1.5;
        }
        .footer {
            text-align: center;
            font-size: 14px;
            padding: 10px;
            background: #f4f4f4;
            border-top: 1px solid #ccc;
        }
        .form-container {
            padding: 20px;
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 10px;
            margin: 20px 0;
        }
        .form-container input,
        .form-container button {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            font-size: 14px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .form-container button {
            background: #990000;
            color: #fff;
            border: none;
            cursor: pointer;
        }
        .form-container button:hover {
            background: #b30000;
        }
        #image-preview img {
            width: 50px;
            height: 50px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <img src="logo.png" alt="Logo">
                <h1>Integrated Speed E-Marketing<br>مؤسسة السرعة المتكاملة للتسويق الإلكتروني</h1>
            </div>
            <div class="details">
                <label for="quote-date">: التاريخ</label>
                <input type="date" id="quote-date">
                <br>
                <label for="customer-name">: اسم العميل</label>
                <input type="text" id="customer-name">
            </div>
        </div>

        <div class="form-container">
            <h3>إضافة منتج</h3>
            <input type="text" id="item-number" placeholder="رقم القطعة">
            <input type="text" id="item-name" placeholder="اسم الصنف" readonly>
            <div id="image-preview"></div>
            <input type="number" id="item-unit" placeholder="الوحدة">
            <input type="number" id="item-price" placeholder="السعر الفردي">
            <button id="add-item">إضافة المنتج</button>
        </div>

        <table class="table" id="price-table">
            <thead>
                <tr>
                    <th>رقم القطعة</th>
                    <th>الصنف</th>
                    <th>صورة المنتج</th>
                    <th>الوحدة</th>
                    <th>السعر الفردي</th>
                    <th>المجموع</th>
                </tr>
            </thead>
            <tbody>
            </tbody>
        </table>

        <div class="notes">
            <p>تم تحديد هذا السعر من قبل: المدير المسؤول</p>
            <p>هذا هو سعر البضائع المذكورة، حيث يخضع إلى البنود الموضحة أدناه:</p>
            <ul>
                <li>الأسعار المقدمة بالدينار الأردني.</li>
                <li>الأسعار المقدمة شاملة لضريبة المبيعات.</li>
                <li>البيع حسب التغليف الموضح لكل صنف.</li>
                <li>توصيل مجاني.</li>
            </ul>
        </div>

        <div class="footer">
            <p>إذا كان لديك أي استفسارات متعلقة بهذا العرض يمكنك التواصل معنا:</p>
            <div class="contact">
                <p>Madina Munawara Street, Amman, Jordan</p>
                <p><a href="tel:+962789556693">+962 7 8955 6693</a></p>
                <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                <p><a href="https://www.ssmjo.com" target="_blank">www.ssmjo.com</a></p>
            </div>
            <p>وتفضلوا بقبول فائق الاحترام.</p>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button id="save-button">حفظ كصورة</button>
            <button id="print-button">طباعة</button>
        </div>
    </div>

    <script>
        document.getElementById('item-number').addEventListener('input', function() {
            const partNumber = this.value;
        
            if (partNumber.length > 0) {
                fetch(`get_product.php?part_number=${partNumber}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            alert(data.error);
                        } else {
                            document.getElementById('item-name').value = data.product_name;
                            document.getElementById('item-unit').value = data.unit_cost;
                            document.getElementById('item-price').value = data.unit_price;
        
                            // عرض صورة المنتج
                            const imagePreview = document.createElement('img');
                            imagePreview.src = `uploads/${data.product_image}`;
                            imagePreview.style.width = '50px';
                            imagePreview.style.height = '50px';
                            const imageContainer = document.getElementById('image-preview');
                            imageContainer.innerHTML = '';
                            imageContainer.appendChild(imagePreview);
                        }
                    })
                    .catch(error => console.error('Error:', error));
            } else {
                document.getElementById('item-name').value = '';
                document.getElementById('item-unit').value = '';
                document.getElementById('item-price').value = '';
                document.getElementById('image-preview').innerHTML = '';
            }
        });

        document.getElementById('add-item').addEventListener('click', function() {
            const number = document.getElementById('item-number').value;
            const name = document.getElementById('item-name').value;
            const unit = document.getElementById('item-unit').value;
            const price = document.getElementById('item-price').value;
            const imageFile = document.getElementById('item-image').files[0];
        
            if (number && name && unit && price && imageFile) {
                const table = document.getElementById('price-table').querySelector('tbody');
                const row = document.createElement('tr');
        
                const numberCell = document.createElement('td');
                numberCell.textContent = number;
        
                const nameCell = document.createElement('td');
                nameCell.textContent = name;
        
                const imageCell = document.createElement('td');
                const img = document.createElement('img');
                img.src = URL.createObjectURL(imageFile); // استخدام URL.createObjectURL لعرض الصورة
                img.style.width = '50px';
                img.style.height = '50px';
                imageCell.appendChild(img);
        
                const unitCell = document.createElement('td');
                unitCell.textContent = unit;
        
                const priceCell = document.createElement('td');
                priceCell.textContent = price;
        
                const totalCell = document.createElement('td');
                const total = parseFloat(unit) * parseFloat(price);
                totalCell.textContent = total.toFixed(2);
        
                row.appendChild(numberCell);
                row.appendChild(nameCell);
                row.appendChild(imageCell);
                row.appendChild(unitCell);
                row.appendChild(priceCell);
                row.appendChild(totalCell);
        
                table.appendChild(row);
        
                // مسح الحقول بعد الإضافة
                document.getElementById('item-number').value = '';
                document.getElementById('item-name').value = '';
                document.getElementById('item-unit').value = '';
                document.getElementById('item-price').value = '';
                document.getElementById('item-image').value = '';
            } else {
                alert('يرجى ملء جميع الحقول وإضافة صورة المنتج.');
            }
        });

        document.getElementById('print-button').addEventListener('click', function() {
            window.print();
        });

        document.getElementById('save-button').addEventListener('click', function() {
            html2canvas(document.querySelector('.container')).then(canvas => {
                const link = document.createElement('a');
                link.download = 'عرض-الأسعار.png';
                link.href = canvas.toDataURL();
                link.click();
            });
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"></script>
</body>
</html>