<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد مبسط - نظام إدارة عروض الأسعار</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f0f0f0;
            padding: 20px;
            direction: rtl;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
        }
        button:hover {
            background: #0056b3;
        }
        .message {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .links {
            text-align: center;
            margin-top: 20px;
        }
        .links a {
            color: #007bff;
            text-decoration: none;
            margin: 0 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏢 إعداد نظام إدارة عروض الأسعار</h1>
        
        <?php
        $message = '';
        $messageType = '';
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $host = $_POST['host'] ?? 'localhost';
            $username = $_POST['username'] ?? 'root';
            $password = $_POST['password'] ?? '';
            $database = $_POST['database'] ?? 'quote_management_system';
            
            try {
                // محاولة الاتصال
                $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // إنشاء قاعدة البيانات
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                $pdo->exec("USE `$database`");
                
                // إنشاء جدول المستخدمين
                $pdo->exec("CREATE TABLE IF NOT EXISTS users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    email VARCHAR(100) UNIQUE NOT NULL,
                    password VARCHAR(255) NOT NULL,
                    full_name VARCHAR(100) NOT NULL,
                    role ENUM('admin', 'employee') DEFAULT 'employee',
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )");
                
                // إضافة المستخدم الافتراضي
                $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
                $pdo->exec("INSERT IGNORE INTO users (id, username, email, password, full_name, role) VALUES 
                           (1, 'admin', '<EMAIL>', '$hashedPassword', 'مدير النظام', 'admin')");
                
                // إنشاء ملف التكوين
                $configContent = "<?php
class Database {
    private \$host = '$host';
    private \$db_name = '$database';
    private \$username = '$username';
    private \$password = '$password';
    private \$charset = 'utf8mb4';
    private \$conn;

    public function connect() {
        \$this->conn = null;
        try {
            \$dsn = \"mysql:host=\" . \$this->host . \";dbname=\" . \$this->db_name . \";charset=\" . \$this->charset;
            \$options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            \$this->conn = new PDO(\$dsn, \$this->username, \$this->password, \$options);
        } catch(PDOException \$e) {
            error_log(\"Connection Error: \" . \$e->getMessage());
            throw new Exception(\"فشل الاتصال بقاعدة البيانات\");
        }
        return \$this->conn;
    }

    public function disconnect() {
        \$this->conn = null;
    }

    public function getConnection() {
        if (\$this->conn === null) {
            return \$this->connect();
        }
        return \$this->conn;
    }

    public function query(\$sql, \$params = []) {
        try {
            \$stmt = \$this->getConnection()->prepare(\$sql);
            \$stmt->execute(\$params);
            return \$stmt;
        } catch(PDOException \$e) {
            error_log(\"Query Error: \" . \$e->getMessage());
            throw new Exception(\"خطأ في تنفيذ الاستعلام\");
        }
    }

    public function fetchOne(\$sql, \$params = []) {
        \$stmt = \$this->query(\$sql, \$params);
        return \$stmt->fetch();
    }

    public function fetchAll(\$sql, \$params = []) {
        \$stmt = \$this->query(\$sql, \$params);
        return \$stmt->fetchAll();
    }

    public function insert(\$sql, \$params = []) {
        \$this->query(\$sql, \$params);
        return \$this->getConnection()->lastInsertId();
    }

    public function update(\$sql, \$params = []) {
        \$stmt = \$this->query(\$sql, \$params);
        return \$stmt->rowCount();
    }

    public function delete(\$sql, \$params = []) {
        \$stmt = \$this->query(\$sql, \$params);
        return \$stmt->rowCount();
    }

    public function beginTransaction() {
        return \$this->getConnection()->beginTransaction();
    }

    public function commit() {
        return \$this->getConnection()->commit();
    }

    public function rollback() {
        return \$this->getConnection()->rollback();
    }
}

class DB {
    private static \$instance = null;

    public static function getInstance() {
        if (self::\$instance === null) {
            self::\$instance = new Database();
        }
        return self::\$instance;
    }

    public static function connection() {
        return self::getInstance()->getConnection();
    }

    public static function query(\$sql, \$params = []) {
        return self::getInstance()->query(\$sql, \$params);
    }

    public static function fetchOne(\$sql, \$params = []) {
        return self::getInstance()->fetchOne(\$sql, \$params);
    }

    public static function fetchAll(\$sql, \$params = []) {
        return self::getInstance()->fetchAll(\$sql, \$params);
    }

    public static function insert(\$sql, \$params = []) {
        return self::getInstance()->insert(\$sql, \$params);
    }

    public static function update(\$sql, \$params = []) {
        return self::getInstance()->update(\$sql, \$params);
    }

    public static function delete(\$sql, \$params = []) {
        return self::getInstance()->delete(\$sql, \$params);
    }
}
?>";
                
                if (!is_dir('config')) {
                    mkdir('config', 0755, true);
                }
                
                file_put_contents('config/database.php', $configContent);
                
                $message = '✅ تم إعداد النظام بنجاح! يمكنك الآن تسجيل الدخول';
                $messageType = 'success';
                
            } catch (Exception $e) {
                $message = '❌ خطأ: ' . $e->getMessage();
                $messageType = 'error';
            }
        }
        ?>
        
        <?php if ($message): ?>
            <div class="message <?php echo $messageType; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <?php if ($messageType !== 'success'): ?>
        <form method="POST">
            <div class="form-group">
                <label>خادم قاعدة البيانات:</label>
                <input type="text" name="host" value="localhost" required>
            </div>
            
            <div class="form-group">
                <label>اسم المستخدم:</label>
                <input type="text" name="username" value="root" required>
            </div>
            
            <div class="form-group">
                <label>كلمة المرور:</label>
                <input type="password" name="password" placeholder="اتركه فارغاً إذا لم تكن هناك كلمة مرور">
            </div>
            
            <div class="form-group">
                <label>اسم قاعدة البيانات:</label>
                <input type="text" name="database" value="quote_management_system" required>
            </div>
            
            <button type="submit">🚀 إعداد النظام</button>
        </form>
        <?php endif; ?>
        
        <div class="links">
            <a href="start.html">🏠 الصفحة الرئيسية</a>
            <a href="test.php">🔍 اختبار PHP</a>
            <?php if ($messageType === 'success'): ?>
                <a href="login.php">🔐 تسجيل الدخول</a>
            <?php endif; ?>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px; text-align: center;">
            <h3>بيانات الدخول الافتراضية:</h3>
            <p><strong>اسم المستخدم:</strong> admin</p>
            <p><strong>كلمة المرور:</strong> admin123</p>
        </div>
    </div>
</body>
</html>
