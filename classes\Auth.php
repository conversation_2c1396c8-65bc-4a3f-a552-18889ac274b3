<?php
/**
 * نظام المصادقة والجلسات
 * نظام إدارة عروض الأسعار الاحترافي
 */

class Auth {
    private $db;

    public function __construct() {
        $this->initDatabase();
        $this->startSession();
    }

    /**
     * تهيئة قاعدة البيانات
     */
    private function initDatabase() {
        try {
            if (file_exists(__DIR__ . '/../config/database.php')) {
                require_once __DIR__ . '/../config/database.php';
                if (class_exists('DB')) {
                    $this->db = DB::getInstance();
                } elseif (class_exists('Database')) {
                    $this->db = new Database();
                } else {
                    throw new Exception('Database class not found');
                }
            } else {
                throw new Exception('Database config file not found');
            }
        } catch (Exception $e) {
            error_log("Database Init Error: " . $e->getMessage());
            // إعادة توجيه لصفحة الإعداد
            if (!headers_sent()) {
                header('Location: simple_setup.php');
                exit;
            }
        }
    }

    /**
     * بدء الجلسة
     */
    private function startSession() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }

    /**
     * تسجيل دخول المستخدم
     */
    public function login($username, $password) {
        try {
            $sql = "SELECT id, username, email, password, full_name, role, is_active 
                    FROM users 
                    WHERE (username = ? OR email = ?) AND is_active = 1";
            
            $user = $this->db->fetchOne($sql, [$username, $username]);
            
            if ($user && password_verify($password, $user['password'])) {
                // تسجيل الدخول بنجاح
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['full_name'] = $user['full_name'];
                $_SESSION['role'] = $user['role'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['logged_in'] = true;
                $_SESSION['login_time'] = time();
                
                // تسجيل النشاط
                $this->logActivity($user['id'], 'login', 'users', $user['id']);
                
                return [
                    'success' => true,
                    'message' => 'تم تسجيل الدخول بنجاح',
                    'user' => [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'full_name' => $user['full_name'],
                        'role' => $user['role']
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'
                ];
            }
        } catch (Exception $e) {
            error_log("Login Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء تسجيل الدخول'
            ];
        }
    }

    /**
     * تسجيل خروج المستخدم
     */
    public function logout() {
        if (isset($_SESSION['user_id'])) {
            $this->logActivity($_SESSION['user_id'], 'logout', 'users', $_SESSION['user_id']);
        }
        
        session_unset();
        session_destroy();
        
        return [
            'success' => true,
            'message' => 'تم تسجيل الخروج بنجاح'
        ];
    }

    /**
     * التحقق من تسجيل الدخول
     */
    public function isLoggedIn() {
        return isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
    }

    /**
     * الحصول على بيانات المستخدم الحالي
     */
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        return [
            'id' => $_SESSION['user_id'],
            'username' => $_SESSION['username'],
            'full_name' => $_SESSION['full_name'],
            'role' => $_SESSION['role'],
            'email' => $_SESSION['email']
        ];
    }

    /**
     * التحقق من الصلاحيات
     */
    public function hasPermission($permission) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        $role = $_SESSION['role'];
        
        // المدير له جميع الصلاحيات
        if ($role === 'admin') {
            return true;
        }
        
        // صلاحيات الموظف
        $employeePermissions = [
            'view_quotes',
            'create_quotes',
            'edit_own_quotes',
            'view_customers',
            'create_customers',
            'edit_customers',
            'view_products'
        ];
        
        return in_array($permission, $employeePermissions);
    }

    /**
     * إنشاء مستخدم جديد
     */
    public function createUser($data) {
        try {
            // التحقق من عدم وجود المستخدم
            $existingUser = $this->db->fetchOne(
                "SELECT id FROM users WHERE username = ? OR email = ?",
                [$data['username'], $data['email']]
            );
            
            if ($existingUser) {
                return [
                    'success' => false,
                    'message' => 'اسم المستخدم أو البريد الإلكتروني موجود مسبقاً'
                ];
            }
            
            // تشفير كلمة المرور
            $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
            
            $sql = "INSERT INTO users (username, email, password, full_name, role) 
                    VALUES (?, ?, ?, ?, ?)";
            
            $userId = $this->db->insert($sql, [
                $data['username'],
                $data['email'],
                $hashedPassword,
                $data['full_name'],
                $data['role'] ?? 'employee'
            ]);
            
            $this->logActivity($_SESSION['user_id'] ?? null, 'create_user', 'users', $userId);
            
            return [
                'success' => true,
                'message' => 'تم إنشاء المستخدم بنجاح',
                'user_id' => $userId
            ];
            
        } catch (Exception $e) {
            error_log("Create User Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء المستخدم'
            ];
        }
    }

    /**
     * تغيير كلمة المرور
     */
    public function changePassword($userId, $currentPassword, $newPassword) {
        try {
            // التحقق من كلمة المرور الحالية
            $user = $this->db->fetchOne("SELECT password FROM users WHERE id = ?", [$userId]);
            
            if (!$user || !password_verify($currentPassword, $user['password'])) {
                return [
                    'success' => false,
                    'message' => 'كلمة المرور الحالية غير صحيحة'
                ];
            }
            
            // تحديث كلمة المرور
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $this->db->update("UPDATE users SET password = ? WHERE id = ?", [$hashedPassword, $userId]);
            
            $this->logActivity($userId, 'change_password', 'users', $userId);
            
            return [
                'success' => true,
                'message' => 'تم تغيير كلمة المرور بنجاح'
            ];
            
        } catch (Exception $e) {
            error_log("Change Password Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء تغيير كلمة المرور'
            ];
        }
    }

    /**
     * تسجيل النشاط
     */
    private function logActivity($userId, $action, $tableName = null, $recordId = null, $oldValues = null, $newValues = null) {
        try {
            $sql = "INSERT INTO activity_log (user_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            
            $this->db->insert($sql, [
                $userId,
                $action,
                $tableName,
                $recordId,
                $oldValues ? json_encode($oldValues) : null,
                $newValues ? json_encode($newValues) : null,
                $_SERVER['REMOTE_ADDR'] ?? null,
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ]);
        } catch (Exception $e) {
            error_log("Activity Log Error: " . $e->getMessage());
        }
    }

    /**
     * إعادة توجيه إذا لم يكن مسجل دخول
     */
    public function requireLogin($redirectTo = 'login.php') {
        if (!$this->isLoggedIn()) {
            header("Location: $redirectTo");
            exit;
        }
    }

    /**
     * إعادة توجيه إذا لم يكن لديه صلاحية
     */
    public function requirePermission($permission, $redirectTo = 'dashboard.php') {
        if (!$this->hasPermission($permission)) {
            header("Location: $redirectTo?error=no_permission");
            exit;
        }
    }
}
