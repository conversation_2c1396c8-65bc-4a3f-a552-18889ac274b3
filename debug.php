<?php
/**
 * ملف تشخيص المشاكل
 * نظام إدارة عروض الأسعار الاحترافي
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

echo "<h1>تشخيص مشاكل النظام</h1>";
echo "<hr>";

// 1. التحقق من إصدار PHP
echo "<h2>1. معلومات PHP</h2>";
echo "إصدار PHP: " . PHP_VERSION . "<br>";
echo "نظام التشغيل: " . PHP_OS . "<br>";
echo "الخادم: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "<br>";

// 2. التحقق من الامتدادات المطلوبة
echo "<h2>2. الامتدادات المطلوبة</h2>";
$required_extensions = ['pdo', 'pdo_mysql', 'gd', 'curl', 'mbstring', 'json'];

foreach ($required_extensions as $ext) {
    $status = extension_loaded($ext) ? '✅ مثبت' : '❌ غير مثبت';
    echo "$ext: $status<br>";
}
echo "<br>";

// 3. التحقق من الملفات المطلوبة
echo "<h2>3. الملفات المطلوبة</h2>";
$required_files = [
    'config/database.php',
    'classes/Auth.php',
    'classes/Customer.php',
    'classes/Quote.php',
    'database_setup.sql'
];

foreach ($required_files as $file) {
    $status = file_exists($file) ? '✅ موجود' : '❌ غير موجود';
    echo "$file: $status<br>";
}
echo "<br>";

// 4. التحقق من صلاحيات المجلدات
echo "<h2>4. صلاحيات المجلدات</h2>";
$required_dirs = ['uploads', 'assets', 'config'];

foreach ($required_dirs as $dir) {
    if (!is_dir($dir)) {
        echo "$dir: ❌ غير موجود<br>";
    } else {
        $writable = is_writable($dir) ? '✅ قابل للكتابة' : '❌ غير قابل للكتابة';
        echo "$dir: $writable<br>";
    }
}
echo "<br>";

// 5. اختبار الاتصال بقاعدة البيانات
echo "<h2>5. اختبار قاعدة البيانات</h2>";

if (file_exists('config/database.php')) {
    try {
        require_once 'config/database.php';
        $db = new Database();
        $conn = $db->connect();
        echo "✅ الاتصال بقاعدة البيانات ناجح<br>";
        
        // اختبار الجداول
        $tables = ['users', 'customers', 'products', 'quotes', 'settings'];
        foreach ($tables as $table) {
            try {
                $stmt = $conn->prepare("SELECT COUNT(*) FROM $table");
                $stmt->execute();
                $count = $stmt->fetchColumn();
                echo "جدول $table: ✅ موجود ($count سجل)<br>";
            } catch (Exception $e) {
                echo "جدول $table: ❌ غير موجود أو خطأ<br>";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ خطأ في الاتصال: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ ملف إعدادات قاعدة البيانات غير موجود<br>";
}
echo "<br>";

// 6. معلومات الخادم
echo "<h2>6. معلومات الخادم</h2>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Script Name: " . $_SERVER['SCRIPT_NAME'] . "<br>";
echo "Current Directory: " . getcwd() . "<br>";
echo "Memory Limit: " . ini_get('memory_limit') . "<br>";
echo "Max Execution Time: " . ini_get('max_execution_time') . "<br>";
echo "Upload Max Filesize: " . ini_get('upload_max_filesize') . "<br>";
echo "<br>";

// 7. اختبار كتابة الملفات
echo "<h2>7. اختبار كتابة الملفات</h2>";
$test_file = 'test_write.txt';
if (file_put_contents($test_file, 'test')) {
    echo "✅ يمكن كتابة الملفات<br>";
    unlink($test_file);
} else {
    echo "❌ لا يمكن كتابة الملفات<br>";
}

// 8. التوصيات
echo "<h2>8. التوصيات لحل المشاكل</h2>";

if (!file_exists('config/database.php')) {
    echo "🔧 <strong>المشكلة الرئيسية:</strong> ملف إعدادات قاعدة البيانات غير موجود<br>";
    echo "📋 <strong>الحل:</strong><br>";
    echo "1. انتقل إلى: <a href='install.php'>install.php</a> لتشغيل معالج التثبيت<br>";
    echo "2. أو قم بإنشاء قاعدة البيانات يدوياً باستخدام: <a href='setup_database.php'>setup_database.php</a><br>";
    echo "<br>";
}

echo "<hr>";
echo "<h3>روابط مفيدة:</h3>";
echo "<a href='install.php'>معالج التثبيت</a> | ";
echo "<a href='setup_database.php'>إعداد قاعدة البيانات</a> | ";
echo "<a href='login.php'>تسجيل الدخول</a>";

?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: #f5f5f5;
}

h1, h2, h3 {
    color: #333;
}

h1 {
    background: #007bff;
    color: white;
    padding: 15px;
    border-radius: 5px;
}

h2 {
    background: #28a745;
    color: white;
    padding: 10px;
    border-radius: 3px;
    margin-top: 20px;
}

a {
    color: #007bff;
    text-decoration: none;
    padding: 5px 10px;
    background: #e7f3ff;
    border-radius: 3px;
    margin: 2px;
    display: inline-block;
}

a:hover {
    background: #007bff;
    color: white;
}
</style>
