<?php
// بيانات الاتصال بقاعدة البيانات
$servername = "sql103.infinityfree.com";
$username = "if0_38649861"; // اسم المستخدم الافتراضي في XAMPP
$password = "GiQo3LaCLiz"; // كلمة المرور الافتراضية في XAMPP
$dbname = "if0_38649861_ssmn";

// إنشاء الاتصال
$conn = new mysqli($servername, $username, $password, $dbname);

// التحقق من الاتصال
if ($conn->connect_error) {
    die("فشل الاتصال: " . $conn->connect_error);
}

// جمع البيانات من النموذج
$part_number = $_POST['part_number'];
$product_name = $_POST['product_name'];
$unit_price = $_POST['unit_price'];
$unit_cost = $_POST['unit_cost'];

// التعامل مع تحميل الصورة
if (isset($_FILES['product_image']) && $_FILES['product_image']['error'] == 0) {
    $target_dir = "uploads/"; // المجلد الذي سيتم حفظ الصور فيه
    if (!is_dir($target_dir)) {
        mkdir($target_dir, 0777, true); // إنشاء المجلد إذا لم يكن موجودًا
    }

    $target_file = $target_dir . basename($_FILES["product_image"]["name"]);
    $imageFileType = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));

    // التحقق من أن الملف هو صورة
    $check = getimagesize($_FILES["product_image"]["tmp_name"]);
    if ($check === false) {
        die("الملف المرفوع ليس صورة.");
    }

    // التحقق من أن الملف غير موجود مسبقًا
    if (file_exists($target_file)) {
        die("الملف موجود مسبقًا.");
    }

    // التحقق من حجم الملف (5MB كحد أقصى)
    if ($_FILES["product_image"]["size"] > 5000000) {
        die("حجم الملف كبير جدًا.");
    }

    // السماح بأنواع معينة من الصور
    if (!in_array($imageFileType, ["jpg", "jpeg", "png", "gif"])) {
        die("نوع الملف غير مدعوم. الرجاء تحميل صورة بصيغة JPG, JPEG, PNG, أو GIF.");
    }

    // تحميل الملف
    if (move_uploaded_file($_FILES["product_image"]["tmp_name"], $target_file)) {
        // إدخال البيانات في قاعدة البيانات
        $sql = "INSERT INTO products (part_number, product_name, product_image, unit_price, unit_cost)
                VALUES ('$part_number', '$product_name', '$target_file', $unit_price, $unit_cost)";

        if ($conn->query($sql) === TRUE) {
            echo "تم إضافة المنتج بنجاح!";
        } else {
            echo "خطأ: " . $sql . "<br>" . $conn->error;
        }
    } else {
        echo "حدث خطأ أثناء تحميل الصورة.";
    }
} else {
    echo "لم يتم تحميل أي صورة.";
}

// إغلاق الاتصال
$conn->close();
?>