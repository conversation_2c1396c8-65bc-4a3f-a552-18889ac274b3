<?php
/**
 * مولد PDF لعروض الأسعار
 * نظام إدارة عروض الأسعار الاحترافي
 */

class PDFGenerator {
    private $settings;

    public function __construct() {
        try {
            $this->settings = new Settings();
        } catch (Exception $e) {
            error_log("PDFGenerator Init Error: " . $e->getMessage());
            // يمكن استخدام إعدادات افتراضية
            $this->settings = null;
        }
    }

    /**
     * إنشاء PDF لعرض الأسعار
     */
    public function generateQuotePDF($quote) {
        try {
            // إنشاء HTML للعرض
            $html = $this->generateQuoteHTML($quote);
            
            // تحويل HTML إلى PDF باستخدام مكتبة بديلة بسيطة
            $pdfContent = $this->htmlToPDF($html);
            
            return [
                'success' => true,
                'content' => $pdfContent,
                'filename' => 'quote_' . $quote['quote_number'] . '.pdf'
            ];
            
        } catch (Exception $e) {
            error_log("Generate PDF Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء ملف PDF'
            ];
        }
    }

    /**
     * إنشاء HTML لعرض الأسعار
     */
    private function generateQuoteHTML($quote) {
        $companySettings = $this->settings->getCompanySettings();
        $systemSettings = $this->settings->getSystemSettings();
        
        $html = '
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>عرض أسعار رقم ' . htmlspecialchars($quote['quote_number']) . '</title>
            <style>
                @import url("https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap");
                
                * {
                    font-family: "Cairo", sans-serif;
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }
                
                body {
                    font-size: 14px;
                    line-height: 1.6;
                    color: #333;
                    background: white;
                }
                
                .container {
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 20px;
                }
                
                .header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 30px;
                    padding-bottom: 20px;
                    border-bottom: 3px solid #2c3e50;
                }
                
                .company-info {
                    flex: 1;
                }
                
                .company-name {
                    font-size: 24px;
                    font-weight: 700;
                    color: #2c3e50;
                    margin-bottom: 10px;
                }
                
                .company-details {
                    font-size: 12px;
                    color: #666;
                    line-height: 1.4;
                }
                
                .logo {
                    width: 100px;
                    height: 100px;
                    object-fit: contain;
                }
                
                .quote-title {
                    text-align: center;
                    font-size: 28px;
                    font-weight: 700;
                    color: #2c3e50;
                    margin: 30px 0;
                    padding: 15px;
                    background: #f8f9fa;
                    border-radius: 10px;
                }
                
                .quote-info {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 30px;
                    margin-bottom: 30px;
                }
                
                .info-section {
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 10px;
                }
                
                .info-title {
                    font-size: 16px;
                    font-weight: 600;
                    color: #2c3e50;
                    margin-bottom: 15px;
                    border-bottom: 2px solid #3498db;
                    padding-bottom: 5px;
                }
                
                .info-item {
                    margin-bottom: 8px;
                    display: flex;
                    justify-content: space-between;
                }
                
                .info-label {
                    font-weight: 600;
                    color: #555;
                }
                
                .info-value {
                    color: #333;
                }
                
                .items-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 30px 0;
                    background: white;
                    border-radius: 10px;
                    overflow: hidden;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                
                .items-table th {
                    background: #2c3e50;
                    color: white;
                    padding: 15px 10px;
                    text-align: center;
                    font-weight: 600;
                }
                
                .items-table td {
                    padding: 12px 10px;
                    text-align: center;
                    border-bottom: 1px solid #eee;
                }
                
                .items-table tr:nth-child(even) {
                    background: #f8f9fa;
                }
                
                .items-table tr:hover {
                    background: #e3f2fd;
                }
                
                .totals {
                    margin-top: 30px;
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 10px;
                }
                
                .total-row {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 10px;
                    padding: 5px 0;
                }
                
                .total-label {
                    font-weight: 600;
                    color: #555;
                }
                
                .total-value {
                    font-weight: 600;
                    color: #333;
                }
                
                .final-total {
                    border-top: 2px solid #2c3e50;
                    padding-top: 15px;
                    margin-top: 15px;
                }
                
                .final-total .total-label,
                .final-total .total-value {
                    font-size: 18px;
                    font-weight: 700;
                    color: #2c3e50;
                }
                
                .notes {
                    margin-top: 30px;
                    padding: 20px;
                    background: #fff3cd;
                    border-radius: 10px;
                    border-right: 5px solid #ffc107;
                }
                
                .notes-title {
                    font-weight: 600;
                    color: #856404;
                    margin-bottom: 10px;
                }
                
                .notes-content {
                    color: #856404;
                    line-height: 1.6;
                }
                
                .terms {
                    margin-top: 30px;
                    padding: 20px;
                    background: #d1ecf1;
                    border-radius: 10px;
                    border-right: 5px solid #17a2b8;
                }
                
                .terms-title {
                    font-weight: 600;
                    color: #0c5460;
                    margin-bottom: 10px;
                }
                
                .terms-content {
                    color: #0c5460;
                    line-height: 1.6;
                }
                
                .footer {
                    margin-top: 50px;
                    text-align: center;
                    padding-top: 20px;
                    border-top: 2px solid #2c3e50;
                    color: #666;
                    font-size: 12px;
                }
                
                .signature-section {
                    margin-top: 40px;
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 50px;
                }
                
                .signature-box {
                    text-align: center;
                    padding: 20px;
                    border: 2px dashed #ccc;
                    border-radius: 10px;
                }
                
                .signature-title {
                    font-weight: 600;
                    color: #555;
                    margin-bottom: 30px;
                }
                
                .signature-line {
                    border-bottom: 2px solid #333;
                    margin: 20px 0;
                    height: 40px;
                }
                
                .signature-label {
                    font-size: 12px;
                    color: #666;
                }
                
                @media print {
                    body {
                        font-size: 12px;
                    }
                    
                    .container {
                        padding: 10px;
                    }
                    
                    .quote-title {
                        font-size: 24px;
                        margin: 20px 0;
                    }
                    
                    .signature-section {
                        page-break-inside: avoid;
                    }
                }
            </style>
        </head>
        <body>
            <div class="container">
                <!-- Header -->
                <div class="header">
                    <div class="company-info">
                        <div class="company-name">' . htmlspecialchars($companySettings['company_name']) . '</div>
                        <div class="company-details">
                            ' . htmlspecialchars($companySettings['company_address']) . '<br>
                            هاتف: ' . htmlspecialchars($companySettings['company_phone']) . '<br>
                            بريد إلكتروني: ' . htmlspecialchars($companySettings['company_email']) . '<br>
                            موقع إلكتروني: ' . htmlspecialchars($companySettings['company_website']) . '
                        </div>
                    </div>';
        
        if ($companySettings['logo_path'] && file_exists($companySettings['logo_path'])) {
            $html .= '<img src="' . $companySettings['logo_path'] . '" alt="شعار الشركة" class="logo">';
        }
        
        $html .= '
                </div>
                
                <!-- Quote Title -->
                <div class="quote-title">
                    عرض أسعار رقم: ' . htmlspecialchars($quote['quote_number']) . '
                </div>
                
                <!-- Quote Info -->
                <div class="quote-info">
                    <div class="info-section">
                        <div class="info-title">بيانات العميل</div>
                        <div class="info-item">
                            <span class="info-label">الاسم:</span>
                            <span class="info-value">' . htmlspecialchars($quote['customer_name']) . '</span>
                        </div>';
        
        if ($quote['customer_company']) {
            $html .= '
                        <div class="info-item">
                            <span class="info-label">الشركة:</span>
                            <span class="info-value">' . htmlspecialchars($quote['customer_company']) . '</span>
                        </div>';
        }
        
        if ($quote['customer_phone']) {
            $html .= '
                        <div class="info-item">
                            <span class="info-label">الهاتف:</span>
                            <span class="info-value">' . htmlspecialchars($quote['customer_phone']) . '</span>
                        </div>';
        }
        
        if ($quote['customer_email']) {
            $html .= '
                        <div class="info-item">
                            <span class="info-label">البريد الإلكتروني:</span>
                            <span class="info-value">' . htmlspecialchars($quote['customer_email']) . '</span>
                        </div>';
        }
        
        if ($quote['customer_address']) {
            $html .= '
                        <div class="info-item">
                            <span class="info-label">العنوان:</span>
                            <span class="info-value">' . htmlspecialchars($quote['customer_address']) . '</span>
                        </div>';
        }
        
        $html .= '
                    </div>
                    
                    <div class="info-section">
                        <div class="info-title">بيانات العرض</div>
                        <div class="info-item">
                            <span class="info-label">تاريخ العرض:</span>
                            <span class="info-value">' . date('Y/m/d', strtotime($quote['quote_date'])) . '</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">صالح حتى:</span>
                            <span class="info-value">' . date('Y/m/d', strtotime($quote['valid_until'])) . '</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">العملة:</span>
                            <span class="info-value">' . htmlspecialchars($systemSettings['currency']) . '</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">نسبة الضريبة:</span>
                            <span class="info-value">' . number_format($quote['tax_rate'], 1) . '%</span>
                        </div>
                    </div>
                </div>
                
                <!-- Items Table -->
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>م</th>
                            <th>الوصف</th>
                            <th>الكمية</th>
                            <th>سعر الوحدة</th>
                            <th>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>';
        
        $itemNumber = 1;
        foreach ($quote['items'] as $item) {
            $html .= '
                        <tr>
                            <td>' . $itemNumber++ . '</td>
                            <td>' . htmlspecialchars($item['item_description']) . '</td>
                            <td>' . number_format($item['quantity'], 2) . '</td>
                            <td>' . number_format($item['unit_price'], 2) . ' ' . htmlspecialchars($systemSettings['currency_symbol']) . '</td>
                            <td>' . number_format($item['line_total'], 2) . ' ' . htmlspecialchars($systemSettings['currency_symbol']) . '</td>
                        </tr>';
        }
        
        $html .= '
                    </tbody>
                </table>
                
                <!-- Totals -->
                <div class="totals">
                    <div class="total-row">
                        <span class="total-label">المجموع الفرعي:</span>
                        <span class="total-value">' . number_format($quote['subtotal'], 2) . ' ' . htmlspecialchars($systemSettings['currency_symbol']) . '</span>
                    </div>';
        
        if ($quote['discount_amount'] > 0) {
            $html .= '
                    <div class="total-row">
                        <span class="total-label">الخصم (' . number_format($quote['discount_rate'], 1) . '%):</span>
                        <span class="total-value">-' . number_format($quote['discount_amount'], 2) . ' ' . htmlspecialchars($systemSettings['currency_symbol']) . '</span>
                    </div>';
        }
        
        if ($quote['tax_amount'] > 0) {
            $html .= '
                    <div class="total-row">
                        <span class="total-label">ضريبة القيمة المضافة (' . number_format($quote['tax_rate'], 1) . '%):</span>
                        <span class="total-value">' . number_format($quote['tax_amount'], 2) . ' ' . htmlspecialchars($systemSettings['currency_symbol']) . '</span>
                    </div>';
        }
        
        $html .= '
                    <div class="total-row final-total">
                        <span class="total-label">الإجمالي النهائي:</span>
                        <span class="total-value">' . number_format($quote['total_amount'], 2) . ' ' . htmlspecialchars($systemSettings['currency_symbol']) . '</span>
                    </div>
                </div>';
        
        // Notes
        if ($quote['notes']) {
            $html .= '
                <div class="notes">
                    <div class="notes-title">ملاحظات:</div>
                    <div class="notes-content">' . nl2br(htmlspecialchars($quote['notes'])) . '</div>
                </div>';
        }
        
        // Terms and Conditions
        if ($quote['terms_conditions']) {
            $html .= '
                <div class="terms">
                    <div class="terms-title">الشروط والأحكام:</div>
                    <div class="terms-content">' . nl2br(htmlspecialchars($quote['terms_conditions'])) . '</div>
                </div>';
        }
        
        $html .= '
                <!-- Signature Section -->
                <div class="signature-section">
                    <div class="signature-box">
                        <div class="signature-title">توقيع العميل</div>
                        <div class="signature-line"></div>
                        <div class="signature-label">الاسم والتوقيع</div>
                    </div>
                    <div class="signature-box">
                        <div class="signature-title">توقيع الشركة</div>
                        <div class="signature-line"></div>
                        <div class="signature-label">الاسم والتوقيع</div>
                    </div>
                </div>
                
                <!-- Footer -->
                <div class="footer">
                    <p>شكراً لكم لاختياركم خدماتنا</p>
                    <p>تم إنشاء هذا العرض بواسطة نظام إدارة عروض الأسعار الاحترافي</p>
                </div>
            </div>
        </body>
        </html>';
        
        return $html;
    }

    /**
     * تحويل HTML إلى PDF (نسخة مبسطة)
     */
    private function htmlToPDF($html) {
        // هذه دالة مبسطة - في التطبيق الحقيقي يجب استخدام مكتبة مثل TCPDF أو DomPDF
        // لكن للتوضيح سنعيد HTML كما هو
        return $html;
    }

    /**
     * حفظ PDF في ملف
     */
    public function savePDFToFile($quote, $filepath) {
        try {
            $result = $this->generateQuotePDF($quote);
            
            if ($result['success']) {
                file_put_contents($filepath, $result['content']);
                return [
                    'success' => true,
                    'filepath' => $filepath
                ];
            } else {
                return $result;
            }
            
        } catch (Exception $e) {
            error_log("Save PDF Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء حفظ ملف PDF'
            ];
        }
    }
}
