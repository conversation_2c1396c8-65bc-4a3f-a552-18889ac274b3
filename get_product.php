<?php
header('Content-Type: application/json');

$servername = "sql103.infinityfree.com";
$username = "if0_38649861"; // اسم المستخدم الافتراضي في XAMPP
$password = "GiQo3LaCLiz"; // كلمة المرور الافتراضية في XAMPP
$dbname = "if0_38649861_ssmn";
// إنشاء الاتصال
$conn = new mysqli($servername, $username, $password, $dbname);

// التحقق من الاتصال
if ($conn->connect_error) {
    die(json_encode(['error' => 'فشل الاتصال بقاعدة البيانات']));
}

$partNumber = $_GET['part_number']; // الحصول على رقم القطعة من الطلب

// استعلام لجلب بيانات المنتج
$sql = "SELECT product_name, product_image, unit_price, unit_cost FROM products WHERE part_number = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $partNumber);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $row = $result->fetch_assoc();
    echo json_encode($row);
} else {
    echo json_encode(['error' => 'المنتج غير موجود']);
}

$stmt->close();
$conn->close();
?>