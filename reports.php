<?php
/**
 * صفحة التقارير والإحصائيات
 * نظام إدارة عروض الأسعار الاحترافي
 */

// التحقق من وجود ملف التكوين أولاً
if (!file_exists('config/database.php')) {
    header('Location: simple_setup.php');
    exit;
}

try {
    require_once 'classes/Auth.php';
    require_once 'classes/Quote.php';
    require_once 'classes/Customer.php';
    require_once 'classes/Product.php';

    $auth = new Auth();
    $auth->requireLogin();

    $user = $auth->getCurrentUser();
    $quote = new Quote();
    $customer = new Customer();
    $product = new Product();
} catch (Exception $e) {
    header('Location: simple_setup.php?error=classes_failed');
    exit;
}

// الحصول على الإحصائيات
$totalQuotes = count($quote->getAll());
$totalCustomers = count($customer->getAll());
$totalProducts = count($product->getAll());

// إحصائيات عروض الأسعار حسب الحالة
$quotesByStatus = [
    'draft' => 0,
    'sent' => 0,
    'accepted' => 0,
    'rejected' => 0,
    'expired' => 0
];

$allQuotes = $quote->getAll();
foreach ($allQuotes as $q) {
    if (isset($quotesByStatus[$q['status']])) {
        $quotesByStatus[$q['status']]++;
    }
}

// إحصائيات مالية
$totalValue = array_sum(array_column($allQuotes, 'total_amount'));
$acceptedQuotes = array_filter($allQuotes, function($q) { return $q['status'] === 'accepted'; });
$acceptedValue = array_sum(array_column($acceptedQuotes, 'total_amount'));

// إحصائيات شهرية (آخر 6 أشهر)
$monthlyStats = [];
for ($i = 5; $i >= 0; $i--) {
    $month = date('Y-m', strtotime("-$i months"));
    $monthName = date('M Y', strtotime("-$i months"));
    
    $monthQuotes = array_filter($allQuotes, function($q) use ($month) {
        return strpos($q['quote_date'], $month) === 0;
    });
    
    $monthlyStats[] = [
        'month' => $monthName,
        'quotes' => count($monthQuotes),
        'value' => array_sum(array_column($monthQuotes, 'total_amount'))
    ];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير والإحصائيات - نظام إدارة عروض الأسعار</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: transform 0.3s ease;
            height: 100%;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-card .stats-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }
        
        .stats-card .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .stats-card .stats-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        
        .status-progress {
            margin-bottom: 15px;
        }
        
        .status-progress .progress {
            height: 25px;
            border-radius: 15px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4>نظام العروض</h4>
        </div>
        <ul class="sidebar-menu">
            <li><a href="simple_dashboard.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
            <li><a href="quotes.php"><i class="fas fa-file-invoice-dollar"></i> عروض الأسعار</a></li>
            <li><a href="customers.php"><i class="fas fa-users"></i> العملاء</a></li>
            <li><a href="products.php"><i class="fas fa-box"></i> المنتجات</a></li>
            <li><a href="reports.php" class="active"><i class="fas fa-chart-bar"></i> التقارير</a></li>
            <?php if ($user['role'] === 'admin'): ?>
            <li><a href="users.php"><i class="fas fa-user-cog"></i> المستخدمين</a></li>
            <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
            <?php endif; ?>
            <li><a href="simple_logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar">
            <div class="d-flex align-items-center">
                <h5 class="mb-0">التقارير والإحصائيات</h5>
            </div>
            <div class="d-flex align-items-center">
                <span class="me-3">مرحباً، <?php echo htmlspecialchars($user['full_name']); ?></span>
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>الملف الشخصي</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="simple_logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="container-fluid p-4">
            <!-- الإحصائيات الرئيسية -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stats-card" style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);">
                        <div class="stats-icon">
                            <i class="fas fa-file-invoice-dollar"></i>
                        </div>
                        <div class="stats-number"><?php echo $totalQuotes; ?></div>
                        <div class="stats-label">إجمالي العروض</div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stats-card" style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%);">
                        <div class="stats-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stats-number"><?php echo $totalCustomers; ?></div>
                        <div class="stats-label">إجمالي العملاء</div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stats-card" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);">
                        <div class="stats-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stats-number"><?php echo $totalProducts; ?></div>
                        <div class="stats-label">إجمالي المنتجات</div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="stats-card" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);">
                        <div class="stats-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stats-number"><?php echo number_format($totalValue, 0); ?></div>
                        <div class="stats-label">إجمالي القيمة (ر.س)</div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- إحصائيات عروض الأسعار -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-pie me-2"></i>عروض الأسعار حسب الحالة</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="statusChart"></canvas>
                            </div>
                            
                            <!-- شريط التقدم لكل حالة -->
                            <div class="mt-4">
                                <?php
                                $statusLabels = [
                                    'draft' => ['مسودة', '#6c757d'],
                                    'sent' => ['مرسل', '#0d6efd'],
                                    'accepted' => ['مقبول', '#198754'],
                                    'rejected' => ['مرفوض', '#dc3545'],
                                    'expired' => ['منتهي الصلاحية', '#fd7e14']
                                ];
                                
                                foreach ($statusLabels as $status => $info):
                                    $count = $quotesByStatus[$status];
                                    $percentage = $totalQuotes > 0 ? ($count / $totalQuotes) * 100 : 0;
                                ?>
                                <div class="status-progress">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span><?php echo $info[0]; ?></span>
                                        <span><?php echo $count; ?> (<?php echo number_format($percentage, 1); ?>%)</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" style="width: <?php echo $percentage; ?>%; background-color: <?php echo $info[1]; ?>"></div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الإحصائيات الشهرية -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-line me-2"></i>الإحصائيات الشهرية</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="monthlyChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الإحصائيات المالية -->
            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-money-bill-wave me-2"></i>الإحصائيات المالية</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <h3 class="text-primary"><?php echo number_format($totalValue, 2); ?> ر.س</h3>
                                    <p class="text-muted">إجمالي قيمة العروض</p>
                                </div>
                                <div class="col-6">
                                    <h3 class="text-success"><?php echo number_format($acceptedValue, 2); ?> ر.س</h3>
                                    <p class="text-muted">قيمة العروض المقبولة</p>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>معدل القبول</span>
                                    <span><?php echo $totalQuotes > 0 ? number_format((count($acceptedQuotes) / $totalQuotes) * 100, 1) : 0; ?>%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-success" style="width: <?php echo $totalQuotes > 0 ? (count($acceptedQuotes) / $totalQuotes) * 100 : 0; ?>%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أفضل العملاء -->
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-star me-2"></i>أفضل العملاء</h5>
                        </div>
                        <div class="card-body">
                            <?php
                            // حساب أفضل العملاء حسب عدد العروض
                            $customerQuotes = [];
                            foreach ($allQuotes as $q) {
                                $customerId = $q['customer_id'];
                                if (!isset($customerQuotes[$customerId])) {
                                    $customerQuotes[$customerId] = [
                                        'name' => $q['customer_name'],
                                        'count' => 0,
                                        'value' => 0
                                    ];
                                }
                                $customerQuotes[$customerId]['count']++;
                                $customerQuotes[$customerId]['value'] += $q['total_amount'];
                            }
                            
                            // ترتيب حسب عدد العروض
                            uasort($customerQuotes, function($a, $b) {
                                return $b['count'] - $a['count'];
                            });
                            
                            $topCustomers = array_slice($customerQuotes, 0, 5, true);
                            ?>
                            
                            <?php if (empty($topCustomers)): ?>
                                <p class="text-muted text-center">لا توجد بيانات كافية</p>
                            <?php else: ?>
                                <?php foreach ($topCustomers as $customerId => $data): ?>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <strong><?php echo htmlspecialchars($data['name']); ?></strong>
                                        <br>
                                        <small class="text-muted"><?php echo $data['count']; ?> عرض - <?php echo number_format($data['value'], 2); ?> ر.س</small>
                                    </div>
                                    <div class="badge bg-primary">#<?php echo array_search($customerId, array_keys($topCustomers)) + 1; ?></div>
                                </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار التصدير -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-download me-2"></i>تصدير التقارير</h5>
                        </div>
                        <div class="card-body text-center">
                            <a href="export_quotes.php" class="btn btn-outline-success me-2">
                                <i class="fas fa-file-excel me-2"></i>تصدير عروض الأسعار
                            </a>
                            
                            <a href="export_customers.php" class="btn btn-outline-info me-2">
                                <i class="fas fa-file-csv me-2"></i>تصدير العملاء
                            </a>
                            
                            <a href="export_products.php" class="btn btn-outline-warning me-2">
                                <i class="fas fa-file-alt me-2"></i>تصدير المنتجات
                            </a>
                            
                            <button onclick="window.print()" class="btn btn-outline-secondary">
                                <i class="fas fa-print me-2"></i>طباعة التقرير
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // رسم بياني دائري لحالات العروض
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['مسودة', 'مرسل', 'مقبول', 'مرفوض', 'منتهي الصلاحية'],
                datasets: [{
                    data: [
                        <?php echo $quotesByStatus['draft']; ?>,
                        <?php echo $quotesByStatus['sent']; ?>,
                        <?php echo $quotesByStatus['accepted']; ?>,
                        <?php echo $quotesByStatus['rejected']; ?>,
                        <?php echo $quotesByStatus['expired']; ?>
                    ],
                    backgroundColor: [
                        '#6c757d',
                        '#0d6efd',
                        '#198754',
                        '#dc3545',
                        '#fd7e14'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // رسم بياني خطي للإحصائيات الشهرية
        const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
        new Chart(monthlyCtx, {
            type: 'line',
            data: {
                labels: [<?php echo '"' . implode('", "', array_column($monthlyStats, 'month')) . '"'; ?>],
                datasets: [{
                    label: 'عدد العروض',
                    data: [<?php echo implode(', ', array_column($monthlyStats, 'quotes')); ?>],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
</body>
</html>
