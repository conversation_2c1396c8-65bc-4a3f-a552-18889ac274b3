-- نظام إدارة عروض الأسعار الاحترافي
-- إعد<PERSON> قاعدة البيانات

CREATE DATABASE IF NOT EXISTS quote_management_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE quote_management_system;

-- جدول المستخدمين
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'employee') DEFAULT 'employee',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول العملاء
CREATE TABLE customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    company_name VARCHAR(100),
    tax_number VARCHAR(50),
    notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- جدول المنتجات (تحديث الجدول الموجود)
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    part_number VARCHAR(50) UNIQUE NOT NULL,
    product_name VARCHAR(200) NOT NULL,
    description TEXT,
    product_image VARCHAR(255),
    unit_price DECIMAL(10,2) NOT NULL,
    unit_cost DECIMAL(10,2),
    category VARCHAR(100),
    unit_type VARCHAR(50) DEFAULT 'قطعة',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول عروض الأسعار
CREATE TABLE quotes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    quote_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id INT NOT NULL,
    quote_date DATE NOT NULL,
    valid_until DATE,
    status ENUM('draft', 'sent', 'approved', 'rejected', 'expired') DEFAULT 'draft',
    subtotal DECIMAL(12,2) DEFAULT 0,
    tax_rate DECIMAL(5,2) DEFAULT 0,
    tax_amount DECIMAL(12,2) DEFAULT 0,
    discount_rate DECIMAL(5,2) DEFAULT 0,
    discount_amount DECIMAL(12,2) DEFAULT 0,
    total_amount DECIMAL(12,2) DEFAULT 0,
    notes TEXT,
    terms_conditions TEXT,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- جدول بنود عروض الأسعار
CREATE TABLE quote_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    quote_id INT NOT NULL,
    product_id INT,
    item_description VARCHAR(255) NOT NULL,
    quantity DECIMAL(10,2) NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    line_total DECIMAL(12,2) NOT NULL,
    sort_order INT DEFAULT 0,
    FOREIGN KEY (quote_id) REFERENCES quotes(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- جدول إعدادات النظام
CREATE TABLE settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('text', 'number', 'boolean', 'json') DEFAULT 'text',
    description VARCHAR(255),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول سجل الأنشطة
CREATE TABLE activity_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(50),
    record_id INT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- إدراج البيانات الافتراضية

-- إعدادات النظام الافتراضية
INSERT INTO settings (setting_key, setting_value, setting_type, description) VALUES
('company_name', 'شركة الأمثل للتجارة', 'text', 'اسم الشركة'),
('company_address', 'الرياض، المملكة العربية السعودية', 'text', 'عنوان الشركة'),
('company_phone', '+966 11 123 4567', 'text', 'هاتف الشركة'),
('company_email', '<EMAIL>', 'text', 'بريد الشركة الإلكتروني'),
('company_website', 'www.company.com', 'text', 'موقع الشركة'),
('tax_rate', '15', 'number', 'نسبة الضريبة الافتراضية'),
('currency', 'ريال سعودي', 'text', 'العملة'),
('currency_symbol', 'ر.س', 'text', 'رمز العملة'),
('quote_validity_days', '30', 'number', 'مدة صلاحية العرض بالأيام'),
('logo_path', 'assets/images/logo.png', 'text', 'مسار شعار الشركة');

-- مستخدم افتراضي (admin/admin123)
INSERT INTO users (username, email, password, full_name, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'admin');

-- عملاء تجريبيون
INSERT INTO customers (name, phone, email, address, company_name, created_by) VALUES
('أحمد محمد السعيد', '+966501234567', '<EMAIL>', 'الرياض، حي النخيل', 'شركة النخيل للتجارة', 1),
('فاطمة علي الزهراني', '+966502345678', '<EMAIL>', 'جدة، حي الصفا', 'مؤسسة الزهراني', 1),
('محمد عبدالله القحطاني', '+966503456789', '<EMAIL>', 'الدمام، حي الفيصلية', 'شركة القحطاني المحدودة', 1);

-- منتجات تجريبية
INSERT INTO products (part_number, product_name, description, unit_price, unit_cost, category, unit_type) VALUES
('PRD001', 'جهاز كمبيوتر محمول', 'جهاز كمبيوتر محمول عالي الأداء', 2500.00, 2000.00, 'أجهزة كمبيوتر', 'قطعة'),
('PRD002', 'طابعة ليزر', 'طابعة ليزر أحادية اللون', 800.00, 600.00, 'طابعات', 'قطعة'),
('PRD003', 'شاشة عرض 24 بوصة', 'شاشة عرض LED عالية الدقة', 450.00, 350.00, 'شاشات', 'قطعة'),
('PRD004', 'لوحة مفاتيح لاسلكية', 'لوحة مفاتيح لاسلكية مع ماوس', 120.00, 80.00, 'ملحقات', 'قطعة'),
('PRD005', 'كابل شبكة', 'كابل شبكة Cat6 طول 5 متر', 25.00, 15.00, 'كابلات', 'قطعة');

-- عرض أسعار تجريبي
INSERT INTO quotes (quote_number, customer_id, quote_date, valid_until, status, created_by) VALUES
('QT-2024-001', 1, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY), 'draft', 1);

-- بنود العرض التجريبي
INSERT INTO quote_items (quote_id, product_id, item_description, quantity, unit_price, line_total) VALUES
(1, 1, 'جهاز كمبيوتر محمول عالي الأداء', 2, 2500.00, 5000.00),
(1, 3, 'شاشة عرض LED 24 بوصة', 2, 450.00, 900.00),
(1, 4, 'لوحة مفاتيح لاسلكية مع ماوس', 2, 120.00, 240.00);

-- تحديث إجمالي العرض التجريبي
UPDATE quotes SET 
    subtotal = 6140.00,
    tax_rate = 15.00,
    tax_amount = 921.00,
    total_amount = 7061.00
WHERE id = 1;
