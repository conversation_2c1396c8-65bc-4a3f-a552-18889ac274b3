<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة عملاء أردنيين نموذجيين</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            direction: rtl;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .message {
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            text-align: center;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #b3d9ff;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 10px;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background: #218838;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .customer-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .customer-item {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        .customer-item h4 {
            margin: 0 0 10px 0;
            color: #28a745;
        }
        .customer-item p {
            margin: 5px 0;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🇯🇴 إضافة عملاء أردنيين نموذجيين</h1>
        
        <?php
        $message = '';
        $messageType = '';
        $addedCustomers = [];
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_customers'])) {
            try {
                if (!file_exists('config/database.php')) {
                    throw new Exception('ملف قاعدة البيانات غير موجود');
                }
                
                require_once 'config/database.php';
                $db = new Database();
                $conn = $db->connect();
                
                // التحقق من وجود جدول العملاء
                $stmt = $conn->prepare("SHOW TABLES LIKE 'customers'");
                $stmt->execute();
                
                if ($stmt->rowCount() == 0) {
                    // إنشاء جدول العملاء
                    $createCustomers = "CREATE TABLE customers (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        name VARCHAR(100) NOT NULL,
                        company_name VARCHAR(150),
                        email VARCHAR(100),
                        phone VARCHAR(20),
                        address TEXT,
                        city VARCHAR(50),
                        postal_code VARCHAR(10),
                        tax_number VARCHAR(50),
                        notes TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    )";
                    $conn->exec($createCustomers);
                }
                
                // عملاء أردنيين نموذجيين
                $jordanianCustomers = [
                    [
                        'name' => 'أحمد محمد الخالدي',
                        'company_name' => 'شركة الخالدي للتجارة العامة',
                        'email' => '<EMAIL>',
                        'phone' => '+962-79-1234567',
                        'address' => 'شارع الملك عبدالله الثاني، مجمع الأعمال التجاري',
                        'city' => 'عمان',
                        'postal_code' => '11118',
                        'tax_number' => '*********',
                        'notes' => 'عميل مميز، يتعامل في المعدات الصناعية'
                    ],
                    [
                        'name' => 'فاطمة سالم العبدالله',
                        'company_name' => 'مؤسسة العبدالله للمقاولات',
                        'email' => '<EMAIL>',
                        'phone' => '+962-77-9876543',
                        'address' => 'منطقة الجبيهة، شارع الجامعة الأردنية',
                        'city' => 'عمان',
                        'postal_code' => '11942',
                        'tax_number' => '*********',
                        'notes' => 'متخصصة في مشاريع البناء والتشييد'
                    ],
                    [
                        'name' => 'محمد علي الزعبي',
                        'company_name' => 'مجموعة الزعبي التجارية',
                        'email' => '<EMAIL>',
                        'phone' => '+962-78-5555444',
                        'address' => 'شارع مكة المكرمة، مجمع الحسين التجاري',
                        'city' => 'إربد',
                        'postal_code' => '21110',
                        'tax_number' => '*********',
                        'notes' => 'تاجر جملة للمواد الغذائية والاستهلاكية'
                    ],
                    [
                        'name' => 'سارة أحمد النعيمي',
                        'company_name' => 'شركة النعيمي للتكنولوجيا',
                        'email' => '<EMAIL>',
                        'phone' => '+962-79-7777888',
                        'address' => 'منطقة عبدون، مجمع الأعمال الذكية',
                        'city' => 'عمان',
                        'postal_code' => '11183',
                        'tax_number' => '*********',
                        'notes' => 'متخصصة في حلول تكنولوجيا المعلومات'
                    ],
                    [
                        'name' => 'خالد يوسف الحموري',
                        'company_name' => 'مؤسسة الحموري للاستيراد والتصدير',
                        'email' => '<EMAIL>',
                        'phone' => '+962-77-3333222',
                        'address' => 'منطقة الحرة، مجمع التجارة الدولية',
                        'city' => 'الزرقاء',
                        'postal_code' => '13110',
                        'tax_number' => '*********',
                        'notes' => 'مستورد للآلات والمعدات الصناعية'
                    ],
                    [
                        'name' => 'ليلى محمود القاسمي',
                        'company_name' => 'شركة القاسمي للاستشارات الهندسية',
                        'email' => '<EMAIL>',
                        'phone' => '+962-78-9999111',
                        'address' => 'شارع الملكة رانيا، مجمع المهندسين',
                        'city' => 'عمان',
                        'postal_code' => '11196',
                        'tax_number' => '*********',
                        'notes' => 'مكتب استشارات هندسية معتمد'
                    ],
                    [
                        'name' => 'عمر سليمان الطراونة',
                        'company_name' => 'مجموعة الطراونة للضيافة والسياحة',
                        'email' => '<EMAIL>',
                        'phone' => '+962-79-4444555',
                        'address' => 'منطقة العقبة السياحية، شارع الكورنيش',
                        'city' => 'العقبة',
                        'postal_code' => '77110',
                        'tax_number' => '*********',
                        'notes' => 'سلسلة فنادق ومنتجعات سياحية'
                    ],
                    [
                        'name' => 'نور الدين عبدالرحمن',
                        'company_name' => 'شركة عبدالرحمن للصناعات الدوائية',
                        'email' => '<EMAIL>',
                        'phone' => '+962-77-6666777',
                        'address' => 'المدينة الصناعية، منطقة الصناعات الدوائية',
                        'city' => 'عمان',
                        'postal_code' => '11831',
                        'tax_number' => '*********',
                        'notes' => 'مصنع أدوية ومستحضرات طبية'
                    ]
                ];
                
                $addedCount = 0;
                foreach ($jordanianCustomers as $customer) {
                    // التحقق من عدم وجود العميل مسبقاً
                    $stmt = $conn->prepare("SELECT id FROM customers WHERE email = ? OR phone = ?");
                    $stmt->execute([$customer['email'], $customer['phone']]);
                    
                    if ($stmt->rowCount() == 0) {
                        $stmt = $conn->prepare("INSERT INTO customers (name, company_name, email, phone, address, city, postal_code, tax_number, notes) 
                                              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
                        $stmt->execute([
                            $customer['name'],
                            $customer['company_name'],
                            $customer['email'],
                            $customer['phone'],
                            $customer['address'],
                            $customer['city'],
                            $customer['postal_code'],
                            $customer['tax_number'],
                            $customer['notes']
                        ]);
                        $addedCustomers[] = $customer;
                        $addedCount++;
                    }
                }
                
                if ($addedCount > 0) {
                    $message = "✅ تم إضافة {$addedCount} عميل أردني بنجاح!";
                    $messageType = 'success';
                } else {
                    $message = 'ℹ️ جميع العملاء موجودون مسبقاً في النظام';
                    $messageType = 'success';
                }
                
            } catch (Exception $e) {
                $message = '❌ خطأ: ' . $e->getMessage();
                $messageType = 'error';
            }
        }
        ?>
        
        <div class="info">
            <h3>🇯🇴 إضافة عملاء أردنيين نموذجيين</h3>
            <p>سيتم إضافة 8 عملاء أردنيين نموذجيين من مختلف القطاعات:</p>
            <ul>
                <li>🏢 شركات تجارية وصناعية</li>
                <li>🏗️ مؤسسات مقاولات وهندسة</li>
                <li>💻 شركات تكنولوجيا</li>
                <li>🏨 قطاع السياحة والضيافة</li>
                <li>💊 الصناعات الدوائية</li>
            </ul>
        </div>
        
        <?php if ($message): ?>
            <div class="message <?php echo $messageType; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($addedCustomers)): ?>
        <div class="customer-list">
            <h3>✅ العملاء المضافون:</h3>
            <?php foreach ($addedCustomers as $customer): ?>
            <div class="customer-item">
                <h4><?php echo htmlspecialchars($customer['name']); ?></h4>
                <p><strong>الشركة:</strong> <?php echo htmlspecialchars($customer['company_name']); ?></p>
                <p><strong>الهاتف:</strong> <?php echo htmlspecialchars($customer['phone']); ?></p>
                <p><strong>المدينة:</strong> <?php echo htmlspecialchars($customer['city']); ?></p>
                <p><strong>الرقم الضريبي:</strong> <?php echo htmlspecialchars($customer['tax_number']); ?></p>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
        
        <?php if ($messageType !== 'success'): ?>
        <form method="POST" style="text-align: center;">
            <button type="submit" name="add_customers" class="btn">
                👥 إضافة العملاء الأردنيين
            </button>
        </form>
        <?php endif; ?>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="start.html" class="btn btn-secondary">🏠 العودة للصفحة الرئيسية</a>
            <?php if ($messageType === 'success'): ?>
                <a href="customers.php" class="btn">👥 عرض العملاء</a>
                <a href="quote_create.php" class="btn">💰 إنشاء عرض أسعار</a>
            <?php endif; ?>
        </div>
        
        <?php if ($messageType === 'success'): ?>
        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <h4>🎉 تم إضافة العملاء بنجاح!</h4>
            <p>الآن يمكنك:</p>
            <ul>
                <li>إنشاء عروض أسعار للعملاء الجدد</li>
                <li>تعديل بيانات العملاء حسب الحاجة</li>
                <li>البحث في قائمة العملاء</li>
                <li>إضافة عملاء جدد</li>
            </ul>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
