<?php
/**
 * كلاس إدارة المنتجات
 * نظام إدارة عروض الأسعار الاحترافي
 */

class Product {
    private $db;

    public function __construct() {
        $this->initDatabase();
    }

    /**
     * تهيئة قاعدة البيانات
     */
    private function initDatabase() {
        try {
            if (file_exists(__DIR__ . '/../config/database.php')) {
                require_once __DIR__ . '/../config/database.php';
                if (class_exists('DB')) {
                    $this->db = DB::getInstance();
                } elseif (class_exists('Database')) {
                    $this->db = new Database();
                } else {
                    throw new Exception('Database class not found');
                }
            } else {
                throw new Exception('Database config file not found');
            }
        } catch (Exception $e) {
            error_log("Database Init Error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إضافة منتج جديد
     */
    public function create($data) {
        try {
            // التحقق من عدم تكرار رقم القطعة
            $existing = $this->db->fetchOne(
                "SELECT id FROM products WHERE part_number = ?",
                [$data['part_number']]
            );
            
            if ($existing) {
                return [
                    'success' => false,
                    'message' => 'رقم القطعة موجود مسبقاً'
                ];
            }
            
            $sql = "INSERT INTO products (part_number, product_name, description, product_image, 
                    unit_price, unit_cost, category, unit_type) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            
            $productId = $this->db->insert($sql, [
                $data['part_number'],
                $data['product_name'],
                $data['description'] ?? null,
                $data['product_image'] ?? null,
                $data['unit_price'],
                $data['unit_cost'] ?? null,
                $data['category'] ?? null,
                $data['unit_type'] ?? 'قطعة'
            ]);
            
            return [
                'success' => true,
                'message' => 'تم إضافة المنتج بنجاح',
                'product_id' => $productId
            ];
            
        } catch (Exception $e) {
            error_log("Create Product Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء إضافة المنتج'
            ];
        }
    }

    /**
     * تحديث بيانات المنتج
     */
    public function update($id, $data) {
        try {
            // التحقق من عدم تكرار رقم القطعة
            $existing = $this->db->fetchOne(
                "SELECT id FROM products WHERE part_number = ? AND id != ?",
                [$data['part_number'], $id]
            );
            
            if ($existing) {
                return [
                    'success' => false,
                    'message' => 'رقم القطعة موجود مسبقاً'
                ];
            }
            
            $sql = "UPDATE products SET 
                    part_number = ?, product_name = ?, description = ?, 
                    unit_price = ?, unit_cost = ?, category = ?, unit_type = ?";
            
            $params = [
                $data['part_number'],
                $data['product_name'],
                $data['description'] ?? null,
                $data['unit_price'],
                $data['unit_cost'] ?? null,
                $data['category'] ?? null,
                $data['unit_type'] ?? 'قطعة'
            ];
            
            // تحديث الصورة إذا تم تمريرها
            if (isset($data['product_image'])) {
                $sql .= ", product_image = ?";
                $params[] = $data['product_image'];
            }
            
            $sql .= " WHERE id = ?";
            $params[] = $id;
            
            $affected = $this->db->update($sql, $params);
            
            if ($affected > 0) {
                return [
                    'success' => true,
                    'message' => 'تم تحديث بيانات المنتج بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'لم يتم العثور على المنتج'
                ];
            }
            
        } catch (Exception $e) {
            error_log("Update Product Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث بيانات المنتج'
            ];
        }
    }

    /**
     * حذف منتج (إلغاء تفعيل)
     */
    public function delete($id) {
        try {
            $sql = "UPDATE products SET is_active = 0 WHERE id = ?";
            $affected = $this->db->update($sql, [$id]);
            
            if ($affected > 0) {
                return [
                    'success' => true,
                    'message' => 'تم حذف المنتج بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'لم يتم العثور على المنتج'
                ];
            }
            
        } catch (Exception $e) {
            error_log("Delete Product Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف المنتج'
            ];
        }
    }

    /**
     * الحصول على منتج بواسطة ID
     */
    public function getById($id) {
        try {
            $sql = "SELECT * FROM products WHERE id = ? AND is_active = 1";
            return $this->db->fetchOne($sql, [$id]);
            
        } catch (Exception $e) {
            error_log("Get Product Error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * الحصول على منتج بواسطة رقم القطعة
     */
    public function getByPartNumber($partNumber) {
        try {
            $sql = "SELECT * FROM products WHERE part_number = ? AND is_active = 1";
            return $this->db->fetchOne($sql, [$partNumber]);
            
        } catch (Exception $e) {
            error_log("Get Product by Part Number Error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * الحصول على جميع المنتجات
     */
    public function getAll($filters = []) {
        try {
            $sql = "SELECT * FROM products WHERE is_active = 1";
            $params = [];
            
            // تطبيق الفلاتر
            if (!empty($filters['search'])) {
                $sql .= " AND (part_number LIKE ? OR product_name LIKE ? OR description LIKE ?)";
                $searchTerm = '%' . $filters['search'] . '%';
                $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
            }
            
            if (!empty($filters['category'])) {
                $sql .= " AND category = ?";
                $params[] = $filters['category'];
            }
            
            if (!empty($filters['price_min'])) {
                $sql .= " AND unit_price >= ?";
                $params[] = $filters['price_min'];
            }
            
            if (!empty($filters['price_max'])) {
                $sql .= " AND unit_price <= ?";
                $params[] = $filters['price_max'];
            }
            
            // ترتيب النتائج
            $orderBy = $filters['order_by'] ?? 'product_name';
            $orderDir = $filters['order_dir'] ?? 'ASC';
            $sql .= " ORDER BY $orderBy $orderDir";
            
            // تحديد عدد النتائج
            if (!empty($filters['limit'])) {
                $sql .= " LIMIT " . intval($filters['limit']);
                if (!empty($filters['offset'])) {
                    $sql .= " OFFSET " . intval($filters['offset']);
                }
            }
            
            return $this->db->fetchAll($sql, $params);
            
        } catch (Exception $e) {
            error_log("Get All Products Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * البحث عن المنتجات
     */
    public function search($query) {
        try {
            $sql = "SELECT * FROM products 
                    WHERE is_active = 1 
                    AND (part_number LIKE ? OR product_name LIKE ? OR description LIKE ?)
                    ORDER BY product_name ASC
                    LIMIT 20";
            
            $searchTerm = '%' . $query . '%';
            return $this->db->fetchAll($sql, [$searchTerm, $searchTerm, $searchTerm]);
            
        } catch (Exception $e) {
            error_log("Search Products Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * عدد المنتجات
     */
    public function getCount($filters = []) {
        try {
            $sql = "SELECT COUNT(*) as total FROM products WHERE is_active = 1";
            $params = [];
            
            if (!empty($filters['search'])) {
                $sql .= " AND (part_number LIKE ? OR product_name LIKE ? OR description LIKE ?)";
                $searchTerm = '%' . $filters['search'] . '%';
                $params = [$searchTerm, $searchTerm, $searchTerm];
            }
            
            if (!empty($filters['category'])) {
                $sql .= " AND category = ?";
                $params[] = $filters['category'];
            }
            
            $result = $this->db->fetchOne($sql, $params);
            return $result['total'];
            
        } catch (Exception $e) {
            error_log("Get Product Count Error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * الحصول على الفئات
     */
    public function getCategories() {
        try {
            $sql = "SELECT DISTINCT category FROM products 
                    WHERE is_active = 1 AND category IS NOT NULL AND category != ''
                    ORDER BY category ASC";
            
            $result = $this->db->fetchAll($sql);
            return array_column($result, 'category');
            
        } catch (Exception $e) {
            error_log("Get Categories Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * التحقق من صحة البيانات
     */
    public function validate($data, $isUpdate = false) {
        $errors = [];
        
        // التحقق من رقم القطعة
        if (empty($data['part_number']) || strlen(trim($data['part_number'])) < 2) {
            $errors[] = 'رقم القطعة مطلوب ويجب أن يكون أكثر من حرفين';
        }
        
        // التحقق من اسم المنتج
        if (empty($data['product_name']) || strlen(trim($data['product_name'])) < 2) {
            $errors[] = 'اسم المنتج مطلوب ويجب أن يكون أكثر من حرفين';
        }
        
        // التحقق من السعر
        if (empty($data['unit_price']) || !is_numeric($data['unit_price']) || $data['unit_price'] <= 0) {
            $errors[] = 'سعر الوحدة مطلوب ويجب أن يكون رقماً موجباً';
        }
        
        // التحقق من التكلفة
        if (!empty($data['unit_cost']) && (!is_numeric($data['unit_cost']) || $data['unit_cost'] < 0)) {
            $errors[] = 'تكلفة الوحدة يجب أن تكون رقماً غير سالب';
        }
        
        return $errors;
    }

    /**
     * رفع صورة المنتج
     */
    public function uploadImage($file) {
        try {
            $uploadDir = 'uploads/products/';
            
            // إنشاء المجلد إذا لم يكن موجوداً
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }
            
            // التحقق من نوع الملف
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            if (!in_array($file['type'], $allowedTypes)) {
                return [
                    'success' => false,
                    'message' => 'نوع الملف غير مدعوم. يرجى رفع صورة بصيغة JPG, PNG, GIF أو WebP'
                ];
            }
            
            // التحقق من حجم الملف (5MB كحد أقصى)
            if ($file['size'] > 5 * 1024 * 1024) {
                return [
                    'success' => false,
                    'message' => 'حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت'
                ];
            }
            
            // إنشاء اسم فريد للملف
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = uniqid() . '.' . $extension;
            $filepath = $uploadDir . $filename;
            
            // رفع الملف
            if (move_uploaded_file($file['tmp_name'], $filepath)) {
                return [
                    'success' => true,
                    'filepath' => $filepath,
                    'filename' => $filename
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في رفع الملف'
                ];
            }
            
        } catch (Exception $e) {
            error_log("Upload Image Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء رفع الصورة'
            ];
        }
    }

    /**
     * حذف صورة المنتج
     */
    public function deleteImage($filepath) {
        try {
            if (file_exists($filepath)) {
                unlink($filepath);
                return true;
            }
            return false;
            
        } catch (Exception $e) {
            error_log("Delete Image Error: " . $e->getMessage());
            return false;
        }
    }
}
