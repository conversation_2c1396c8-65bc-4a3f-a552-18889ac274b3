/**
 * ملف التصميم الرئيسي
 * نظام إدارة عروض الأسعار الاحترافي
 */

:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 10px;
    --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    font-family: 'Cairo', sans-serif;
}

body {
    background-color: var(--light-color);
    font-size: 14px;
    line-height: 1.6;
}

/* Sidebar Styles */
.sidebar {
    background: var(--primary-color);
    min-height: 100vh;
    width: 250px;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 1000;
    transition: var(--transition);
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar-header {
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
}

.sidebar-header h4 {
    color: white;
    margin: 0;
    font-weight: 600;
    font-size: 1.1rem;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-menu li {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    padding: 1rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
    font-size: 0.9rem;
}

.sidebar-menu a:hover,
.sidebar-menu a.active {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(-5px);
}

.sidebar-menu i {
    width: 20px;
    margin-left: 10px;
    text-align: center;
    font-size: 1rem;
}

/* Main Content */
.main-content {
    margin-right: 250px;
    transition: var(--transition);
    min-height: 100vh;
}

.main-content.expanded {
    margin-right: 70px;
}

/* Top Navbar */
.top-navbar {
    background: white;
    padding: 1rem 1.5rem;
    box-shadow: var(--box-shadow);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 999;
}

.btn-toggle-sidebar {
    background: none;
    border: none;
    color: var(--dark-color);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 5px;
    transition: var(--transition);
}

.btn-toggle-sidebar:hover {
    background: var(--light-color);
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: var(--primary-color);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    font-weight: 600;
    padding: 1rem 1.5rem;
    border: none;
}

.card-body {
    padding: 1.5rem;
}

/* Stats Cards */
.stats-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    text-align: center;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin: 0 auto 1rem;
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.stats-label {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Tables */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--dark-color);
    background: var(--light-color);
    padding: 1rem 0.75rem;
    font-size: 0.9rem;
}

.table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border-color: #e9ecef;
}

.table-hover tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: var(--transition);
    border: none;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background: var(--secondary-color);
    box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover {
    background: #2980b9;
    box-shadow: 0 4px 8px rgba(52, 152, 219, 0.4);
}

.btn-success {
    background: var(--success-color);
    box-shadow: 0 2px 4px rgba(39, 174, 96, 0.3);
}

.btn-success:hover {
    background: #229954;
    box-shadow: 0 4px 8px rgba(39, 174, 96, 0.4);
}

.btn-danger {
    background: var(--danger-color);
    box-shadow: 0 2px 4px rgba(231, 76, 60, 0.3);
}

.btn-danger:hover {
    background: #c0392b;
    box-shadow: 0 4px 8px rgba(231, 76, 60, 0.4);
}

.btn-warning {
    background: var(--warning-color);
    box-shadow: 0 2px 4px rgba(243, 156, 18, 0.3);
}

.btn-warning:hover {
    background: #e67e22;
    box-shadow: 0 4px 8px rgba(243, 156, 18, 0.4);
}

.btn-action {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    margin: 0 0.125rem;
}

/* Forms */
.form-control {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem;
    transition: var(--transition);
    font-size: 0.9rem;
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-floating > .form-control {
    height: auto;
    padding: 1rem 0.75rem;
}

.form-floating > label {
    padding: 1rem 0.75rem;
    font-size: 0.9rem;
}

/* Search Box */
.search-box {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--box-shadow);
}

/* Alerts */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1rem 1.5rem;
    font-weight: 500;
}

.alert-success {
    background: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
}

.alert-danger {
    background: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
}

.alert-warning {
    background: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
}

.alert-info {
    background: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

/* Badges */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
}

/* Pagination */
.pagination .page-link {
    border: none;
    color: var(--secondary-color);
    padding: 0.75rem 1rem;
    margin: 0 0.125rem;
    border-radius: 8px;
    transition: var(--transition);
}

.pagination .page-link:hover {
    background: var(--secondary-color);
    color: white;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: var(--secondary-color);
    border-color: var(--secondary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        transform: translateX(100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .top-navbar {
        padding: 1rem;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .search-box {
        padding: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.8rem;
    }
    
    .btn-action {
        padding: 0.125rem 0.25rem;
        font-size: 0.75rem;
    }
}

@media (max-width: 576px) {
    .stats-number {
        font-size: 1.5rem;
    }
    
    .stats-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
    
    .sidebar-menu a {
        padding: 0.75rem;
        font-size: 0.8rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #1a1a1a;
        --dark-color: #ffffff;
    }
    
    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    
    .card {
        background-color: #2d2d2d;
        color: #ffffff;
    }
    
    .top-navbar {
        background-color: #2d2d2d;
        color: #ffffff;
    }
    
    .search-box {
        background-color: #2d2d2d;
    }
    
    .form-control {
        background-color: #3d3d3d;
        border-color: #4d4d4d;
        color: #ffffff;
    }
    
    .table {
        color: #ffffff;
    }
    
    .table th {
        background-color: #3d3d3d;
        color: #ffffff;
    }
}
