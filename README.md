# نظام إدارة عروض الأسعار الاحترافي

نظام ويب احترافي لإدارة عروض الأسعار باستخدام PHP، مصمم خصيصاً للشركات والمؤسسات التي تحتاج لإنشاء وإدارة عروض الأسعار بطريقة احترافية وفعالة.

## 🌟 المميزات الرئيسية

### 📋 إدارة العملاء
- إضافة وتعديل وحذف العملاء
- البحث السريع في قاعدة بيانات العملاء
- تخزين معلومات شاملة (الاسم، الشركة، الهاتف، البريد الإلكتروني، العنوان)
- تتبع تاريخ التعامل مع كل عميل

### 💰 إدارة عروض الأسعار
- إنشاء عروض أسعار احترافية
- إضافة عدة بنود لكل عرض
- حساب تلقائي للضرائب والخصومات
- تتبع حالة العرض (مسودة، مرسل، موافق عليه، مرفوض)
- ترقيم تلقائي للعروض

### 📦 إدارة المنتجات
- كتالوج شامل للمنتجات والخدمات
- تصنيف المنتجات حسب الفئات
- إدارة الأسعار والتكاليف
- رفع صور المنتجات
- البحث السريع في المنتجات

### 📄 توليد PDF احترافي
- تصميم عروض أسعار احترافية
- دعم الشعار والختم الإلكتروني
- قوالب قابلة للتخصيص
- دعم اللغة العربية والخطوط العربية

### 👥 إدارة المستخدمين
- نظام صلاحيات متقدم (مدير، موظف)
- تسجيل دخول آمن
- تتبع أنشطة المستخدمين
- إدارة كلمات المرور

### ⚙️ إعدادات النظام
- إعدادات الشركة (الاسم، العنوان، الشعار)
- إعدادات الضرائب والعملة
- تخصيص قوالب العروض
- إعدادات البريد الإلكتروني

### 📊 التقارير والإحصائيات
- لوحة تحكم تفاعلية
- إحصائيات العروض والعملاء
- تصدير البيانات إلى Excel
- تقارير مفصلة

## 🛠️ المتطلبات التقنية

- **PHP**: الإصدار 8.0 أو أحدث
- **MySQL**: الإصدار 5.7 أو أحدث
- **Apache/Nginx**: خادم ويب
- **مساحة تخزين**: 100 ميجابايت على الأقل

### المكتبات المطلوبة
- PDO (لقاعدة البيانات)
- GD (لمعالجة الصور)
- cURL (للاتصالات الخارجية)

## 📥 التثبيت

### 1. تحميل الملفات
```bash
git clone https://github.com/your-repo/quote-management-system.git
cd quote-management-system
```

### 2. إعداد قاعدة البيانات
1. إنشاء قاعدة بيانات جديدة في MySQL
2. تشغيل سكريبت إعداد قاعدة البيانات:
```sql
mysql -u username -p database_name < database_setup.sql
```

### 3. تكوين الاتصال
تعديل ملف `config/database.php`:
```php
private $host = 'localhost';
private $db_name = 'quote_management_system';
private $username = 'your_username';
private $password = 'your_password';
```

### 4. إعداد الصلاحيات
```bash
chmod 755 uploads/
chmod 755 assets/images/
chmod 755 exports/
```

### 5. الوصول للنظام
- افتح المتصفح وانتقل إلى: `http://your-domain/login.php`
- بيانات الدخول الافتراضية:
  - **اسم المستخدم**: admin
  - **كلمة المرور**: admin123

## 🚀 الاستخدام

### إنشاء عرض أسعار جديد
1. انتقل إلى "عروض الأسعار" → "إضافة عرض جديد"
2. اختر العميل أو أضف عميل جديد
3. أضف بنود العرض (المنتجات/الخدمات)
4. حدد نسبة الضريبة والخصم
5. احفظ العرض وقم بتوليد PDF

### إدارة العملاء
1. انتقل إلى "العملاء"
2. أضف عميل جديد أو عدل بيانات عميل موجود
3. استخدم البحث للعثور على عملاء محددين

### إعداد النظام
1. انتقل إلى "الإعدادات" (للمديرين فقط)
2. حدث بيانات الشركة والشعار
3. اضبط نسبة الضريبة والعملة
4. خصص قوالب العروض

## 📁 هيكل المشروع

```
quote-management-system/
├── classes/                 # الكلاسات الأساسية
│   ├── Auth.php            # نظام المصادقة
│   ├── Customer.php        # إدارة العملاء
│   ├── Quote.php           # إدارة العروض
│   ├── Product.php         # إدارة المنتجات
│   ├── Settings.php        # إدارة الإعدادات
│   └── PDFGenerator.php    # مولد PDF
├── config/                 # ملفات التكوين
│   └── database.php        # إعدادات قاعدة البيانات
├── assets/                 # الملفات الثابتة
│   ├── css/               # ملفات CSS
│   ├── js/                # ملفات JavaScript
│   └── images/            # الصور والشعارات
├── uploads/               # ملفات المستخدمين
├── exports/               # ملفات التصدير
├── login.php             # صفحة تسجيل الدخول
├── dashboard.php         # لوحة التحكم
├── customers.php         # إدارة العملاء
├── quotes.php            # إدارة العروض
├── products.php          # إدارة المنتجات
├── settings.php          # الإعدادات
└── database_setup.sql    # سكريبت إعداد قاعدة البيانات
```

## 🔒 الأمان

- حماية من حقن SQL باستخدام Prepared Statements
- حماية من هجمات XSS
- تشفير كلمات المرور باستخدام bcrypt
- نظام صلاحيات متقدم
- تسجيل جميع الأنشطة

## 🎨 التخصيص

### تخصيص التصميم
- عدل ملف `assets/css/style.css` لتغيير الألوان والخطوط
- استخدم متغيرات CSS للتخصيص السريع

### إضافة حقول جديدة
1. عدل جدول قاعدة البيانات
2. حدث الكلاس المناسب
3. عدل واجهات المستخدم

## 📞 الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966 11 123 4567

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للمزيد من التفاصيل.

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إرسال Pull Request

## 📈 التطوير المستقبلي

### الميزات المخططة
- تكامل مع WhatsApp Business API
- تطبيق موبايل
- نظام محاسبة متكامل
- تقارير متقدمة
- دعم عدة لغات
- API للتكامل مع أنظمة أخرى

## 🏆 الشكر والتقدير

شكر خاص لجميع المساهمين في تطوير هذا النظام والمكتبات المستخدمة:
- Bootstrap للواجهات
- Font Awesome للأيقونات
- Google Fonts للخطوط العربية

---

**تم تطوير هذا النظام بواسطة فريق التطوير المحترف**

© 2024 نظام إدارة عروض الأسعار الاحترافي. جميع الحقوق محفوظة.
