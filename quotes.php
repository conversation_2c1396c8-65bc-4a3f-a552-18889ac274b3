<?php
/**
 * صفحة قائمة عروض الأسعار
 * نظام إدارة عروض الأسعار الاحترافي
 */

// التحقق من وجود ملف التكوين أولاً
if (!file_exists('config/database.php')) {
    header('Location: simple_setup.php');
    exit;
}

try {
    require_once 'classes/Auth.php';
    require_once 'classes/Quote.php';
    require_once 'classes/Customer.php';

    $auth = new Auth();
    $auth->requireLogin();
    $auth->requirePermission('view_quotes');

    $user = $auth->getCurrentUser();
    $quote = new Quote();
    $customer = new Customer();
} catch (Exception $e) {
    header('Location: simple_setup.php?error=classes_failed');
    exit;
}

// معالجة البحث والفلترة
$search = $_GET['search'] ?? '';
$status = $_GET['status'] ?? '';
$customer_id = $_GET['customer_id'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';

$filters = [
    'search' => $search,
    'status' => $status,
    'customer_id' => $customer_id,
    'date_from' => $date_from,
    'date_to' => $date_to
];

// إذا لم يكن مدير، عرض عروضه فقط
if ($user['role'] !== 'admin') {
    $filters['created_by'] = $user['id'];
}

$quotes = $quote->getAll($filters);
$customers = $customer->getAll();

// معالجة الحذف
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_quote'])) {
    $quoteId = $_POST['quote_id'];
    $result = $quote->delete($quoteId);
    
    if ($result['success']) {
        header('Location: quotes.php?success=deleted');
        exit;
    } else {
        $error = $result['message'];
    }
}

$success = $_GET['success'] ?? '';
$error = $_GET['error'] ?? '';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عروض الأسعار - نظام إدارة عروض الأسعار</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        .status-badge {
            font-size: 0.8rem;
            padding: 0.4rem 0.8rem;
        }
        
        .status-draft { background-color: #6c757d; }
        .status-sent { background-color: #0d6efd; }
        .status-accepted { background-color: #198754; }
        .status-rejected { background-color: #dc3545; }
        .status-expired { background-color: #fd7e14; }
        
        .quote-card {
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .quote-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .filters-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4>نظام العروض</h4>
        </div>
        <ul class="sidebar-menu">
            <li><a href="simple_dashboard.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
            <li><a href="quotes.php" class="active"><i class="fas fa-file-invoice-dollar"></i> عروض الأسعار</a></li>
            <li><a href="customers.php"><i class="fas fa-users"></i> العملاء</a></li>
            <li><a href="products.php"><i class="fas fa-box"></i> المنتجات</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar"></i> التقارير</a></li>
            <?php if ($user['role'] === 'admin'): ?>
            <li><a href="users.php"><i class="fas fa-user-cog"></i> المستخدمين</a></li>
            <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
            <?php endif; ?>
            <li><a href="simple_logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar">
            <div class="d-flex align-items-center">
                <h5 class="mb-0">عروض الأسعار</h5>
            </div>
            <div class="d-flex align-items-center">
                <span class="me-3">مرحباً، <?php echo htmlspecialchars($user['full_name']); ?></span>
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>الملف الشخصي</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="simple_logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="container-fluid p-4">
            <!-- رسائل النجاح والخطأ -->
            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php
                    switch ($success) {
                        case 'created': echo 'تم إنشاء عرض الأسعار بنجاح'; break;
                        case 'updated': echo 'تم تحديث عرض الأسعار بنجاح'; break;
                        case 'deleted': echo 'تم حذف عرض الأسعار بنجاح'; break;
                        case 'sent': echo 'تم إرسال عرض الأسعار بنجاح'; break;
                    }
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- أزرار الإجراءات -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <a href="quote_create.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>عرض أسعار جديد
                    </a>
                    
                    <?php if ($user['role'] === 'admin'): ?>
                    <a href="quotes_export.php" class="btn btn-outline-success">
                        <i class="fas fa-download me-2"></i>تصدير
                    </a>
                    <?php endif; ?>
                </div>
                
                <div class="d-flex align-items-center">
                    <span class="text-muted me-3">إجمالي العروض: <?php echo count($quotes); ?></span>
                    
                    <button class="btn btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#filtersCollapse">
                        <i class="fas fa-filter me-2"></i>فلترة
                    </button>
                </div>
            </div>

            <!-- قسم الفلترة -->
            <div class="collapse" id="filtersCollapse">
                <div class="filters-section">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">البحث</label>
                            <input type="text" name="search" class="form-control" value="<?php echo htmlspecialchars($search); ?>" placeholder="رقم العرض أو اسم العميل">
                        </div>
                        
                        <div class="col-md-2">
                            <label class="form-label">الحالة</label>
                            <select name="status" class="form-select">
                                <option value="">جميع الحالات</option>
                                <option value="draft" <?php echo $status === 'draft' ? 'selected' : ''; ?>>مسودة</option>
                                <option value="sent" <?php echo $status === 'sent' ? 'selected' : ''; ?>>مرسل</option>
                                <option value="accepted" <?php echo $status === 'accepted' ? 'selected' : ''; ?>>مقبول</option>
                                <option value="rejected" <?php echo $status === 'rejected' ? 'selected' : ''; ?>>مرفوض</option>
                                <option value="expired" <?php echo $status === 'expired' ? 'selected' : ''; ?>>منتهي الصلاحية</option>
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <label class="form-label">العميل</label>
                            <select name="customer_id" class="form-select">
                                <option value="">جميع العملاء</option>
                                <?php foreach ($customers as $cust): ?>
                                <option value="<?php echo $cust['id']; ?>" <?php echo $customer_id == $cust['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($cust['name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" name="date_from" class="form-control" value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>
                        
                        <div class="col-md-2">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" name="date_to" class="form-control" value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>
                        
                        <div class="col-md-1 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- قائمة العروض -->
            <?php if (empty($quotes)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-file-invoice-dollar fa-5x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد عروض أسعار</h4>
                    <p class="text-muted">ابدأ بإنشاء عرض أسعار جديد</p>
                    <a href="quote_create.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إنشاء عرض أسعار
                    </a>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($quotes as $quoteItem): ?>
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="card quote-card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">عرض رقم #<?php echo htmlspecialchars($quoteItem['quote_number']); ?></h6>
                                <span class="badge status-badge status-<?php echo $quoteItem['status']; ?>">
                                    <?php
                                    $statusLabels = [
                                        'draft' => 'مسودة',
                                        'sent' => 'مرسل',
                                        'accepted' => 'مقبول',
                                        'rejected' => 'مرفوض',
                                        'expired' => 'منتهي الصلاحية'
                                    ];
                                    echo $statusLabels[$quoteItem['status']] ?? $quoteItem['status'];
                                    ?>
                                </span>
                            </div>
                            
                            <div class="card-body">
                                <div class="mb-2">
                                    <strong>العميل:</strong> <?php echo htmlspecialchars($quoteItem['customer_name']); ?>
                                </div>
                                
                                <div class="mb-2">
                                    <strong>التاريخ:</strong> <?php echo date('Y/m/d', strtotime($quoteItem['quote_date'])); ?>
                                </div>
                                
                                <div class="mb-2">
                                    <strong>صالح حتى:</strong> 
                                    <span class="<?php echo strtotime($quoteItem['valid_until']) < time() ? 'text-danger' : 'text-success'; ?>">
                                        <?php echo date('Y/m/d', strtotime($quoteItem['valid_until'])); ?>
                                    </span>
                                </div>
                                
                                <div class="mb-3">
                                    <strong>الإجمالي:</strong> 
                                    <span class="text-primary fw-bold"><?php echo number_format($quoteItem['total_amount'], 2); ?> ر.س</span>
                                </div>
                                
                                <div class="text-muted small">
                                    أنشأه: <?php echo htmlspecialchars($quoteItem['created_by_name']); ?>
                                </div>
                            </div>
                            
                            <div class="card-footer">
                                <div class="btn-group w-100" role="group">
                                    <a href="quote_view.php?id=<?php echo $quoteItem['id']; ?>" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                    
                                    <?php if ($quoteItem['status'] === 'draft' || $user['role'] === 'admin'): ?>
                                    <a href="quote_edit.php?id=<?php echo $quoteItem['id']; ?>" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-edit"></i> تعديل
                                    </a>
                                    <?php endif; ?>
                                    
                                    <a href="quote_pdf.php?id=<?php echo $quoteItem['id']; ?>" class="btn btn-outline-success btn-sm" target="_blank">
                                        <i class="fas fa-file-pdf"></i> PDF
                                    </a>
                                    
                                    <?php if ($user['role'] === 'admin' || $quoteItem['created_by'] == $user['id']): ?>
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="confirmDelete(<?php echo $quoteItem['id']; ?>, '<?php echo htmlspecialchars($quoteItem['quote_number']); ?>')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Modal تأكيد الحذف -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف عرض الأسعار رقم <strong id="deleteQuoteNumber"></strong>؟</p>
                    <p class="text-danger">لا يمكن التراجع عن هذا الإجراء.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="quote_id" id="deleteQuoteId">
                        <button type="submit" name="delete_quote" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function confirmDelete(quoteId, quoteNumber) {
            document.getElementById('deleteQuoteId').value = quoteId;
            document.getElementById('deleteQuoteNumber').textContent = quoteNumber;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
    </script>
</body>
</html>
