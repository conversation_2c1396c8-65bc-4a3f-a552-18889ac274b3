<?php
header('Content-Type: application/json');

$servername = "localhost";
$username = "root"; // اسم المستخدم الافتراضي في XAMPP
$password = ""; // كلمة المرور الافتراضية في XAMPP
$dbname = "quote_management_system";

// إنشاء الاتصال
$conn = new mysqli($servername, $username, $password, $dbname);

// التحقق من الاتصال
if ($conn->connect_error) {
    die(json_encode(['success' => false, 'message' => 'فشل الاتصال بقاعدة البيانات']));
}

// جمع البيانات من النموذج
$partNumber = $conn->real_escape_string($_POST['part_number']);
$productName = $conn->real_escape_string($_POST['product_name']);
$unitPrice = floatval($_POST['unit_price']); // تحويل إلى رقم عشري
$unitCost = floatval($_POST['unit_cost']); // تحويل إلى رقم عشري

// التعامل مع تحميل الصورة
if (isset($_FILES['product_image']) && $_FILES['product_image']['error'] == 0) {
    $target_dir = "uploads/";
    if (!is_dir($target_dir)) {
        mkdir($target_dir, 0777, true); // إنشاء المجلد إذا لم يكن موجودًا
    }

    // إنشاء اسم فريد للملف لتجنب التكرار
    $fileExtension = strtolower(pathinfo($_FILES["product_image"]["name"], PATHINFO_EXTENSION));
    $uniqueFileName = uniqid() . '.' . $fileExtension;
    $target_file = $target_dir . $uniqueFileName;

    // التحقق من أن الملف هو صورة
    $check = getimagesize($_FILES["product_image"]["tmp_name"]);
    if ($check === false) {
        echo json_encode(['success' => false, 'message' => 'الملف ليس صورة.']);
        exit;
    }

    // التحقق من حجم الملف (5MB كحد أقصى)
    if ($_FILES["product_image"]["size"] > 5000000) {
        echo json_encode(['success' => false, 'message' => 'حجم الملف كبير جدًا.']);
        exit;
    }

    // السماح بأنواع معينة من الصور
    if (!in_array($fileExtension, ["jpg", "jpeg", "png", "gif"])) {
        echo json_encode(['success' => false, 'message' => 'نوع الملف غير مدعوم.']);
        exit;
    }

    // تحميل الملف
    if (move_uploaded_file($_FILES["product_image"]["tmp_name"], $target_file)) {
        // إدخال البيانات في قاعدة البيانات باستخدام استعلام مُعد مسبقًا
        $sql = "INSERT INTO products (part_number, product_name, product_image, unit_price, unit_cost)
                VALUES (?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sssdd", $partNumber, $productName, $target_file, $unitPrice, $unitCost);

        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'تمت إضافة المنتج بنجاح.']);
        } else {
            echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء إضافة المنتج.']);
        }
        $stmt->close();
    } else {
        echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء تحميل الصورة.']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'لم يتم تحميل أي صورة.']);
}

$conn->close();
?>