<?php
/**
 * سكريبت تثبيت النظام
 * نظام إدارة عروض الأسعار الاحترافي
 */

// التحقق من وجود ملف التكوين
if (file_exists('config/database.php')) {
    $message = 'يبدو أن النظام مثبت بالفعل. إذا كنت تريد إعادة التثبيت، احذف ملف config/database.php أولاً.';
    $messageType = 'warning';
} else {
    $message = '';
    $messageType = '';
}

$step = $_GET['step'] ?? 1;
$errors = [];
$success = false;

// معالجة خطوات التثبيت
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 1:
            // التحقق من المتطلبات
            $requirements = checkRequirements();
            if (empty($requirements['errors'])) {
                header('Location: install.php?step=2');
                exit;
            } else {
                $errors = $requirements['errors'];
            }
            break;
            
        case 2:
            // إعداد قاعدة البيانات
            $dbConfig = [
                'host' => $_POST['db_host'] ?? 'localhost',
                'name' => $_POST['db_name'] ?? 'quote_management_system',
                'username' => $_POST['db_username'] ?? 'root',
                'password' => $_POST['db_password'] ?? ''
            ];
            
            $result = setupDatabase($dbConfig);
            if ($result['success']) {
                header('Location: install.php?step=3');
                exit;
            } else {
                $errors[] = $result['message'];
            }
            break;
            
        case 3:
            // إعداد المدير
            $adminData = [
                'username' => $_POST['admin_username'] ?? 'admin',
                'email' => $_POST['admin_email'] ?? '<EMAIL>',
                'password' => $_POST['admin_password'] ?? '',
                'full_name' => $_POST['admin_name'] ?? 'مدير النظام'
            ];
            
            $result = setupAdmin($adminData);
            if ($result['success']) {
                $success = true;
                $message = 'تم تثبيت النظام بنجاح! يمكنك الآن تسجيل الدخول.';
                $messageType = 'success';
            } else {
                $errors[] = $result['message'];
            }
            break;
    }
}

/**
 * التحقق من المتطلبات
 */
function checkRequirements() {
    $errors = [];
    $warnings = [];
    
    // التحقق من إصدار PHP
    if (version_compare(PHP_VERSION, '8.0.0', '<')) {
        $errors[] = 'يتطلب النظام PHP 8.0 أو أحدث. الإصدار الحالي: ' . PHP_VERSION;
    }
    
    // التحقق من الامتدادات المطلوبة
    $requiredExtensions = ['pdo', 'pdo_mysql', 'gd', 'curl', 'mbstring', 'json'];
    foreach ($requiredExtensions as $ext) {
        if (!extension_loaded($ext)) {
            $errors[] = "امتداد PHP مطلوب غير مثبت: $ext";
        }
    }
    
    // التحقق من صلاحيات الكتابة
    $writableDirs = ['uploads', 'assets/images', 'exports'];
    foreach ($writableDirs as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0777, true);
        }
        if (!is_writable($dir)) {
            $warnings[] = "المجلد غير قابل للكتابة: $dir";
        }
    }
    
    return [
        'errors' => $errors,
        'warnings' => $warnings
    ];
}

/**
 * إعداد قاعدة البيانات
 */
function setupDatabase($config) {
    try {
        // الاتصال بقاعدة البيانات
        $dsn = "mysql:host={$config['host']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['username'], $config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // إنشاء قاعدة البيانات
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['name']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `{$config['name']}`");
        
        // تشغيل سكريبت إنشاء الجداول
        $sql = file_get_contents('database_setup.sql');
        $statements = explode(';', $sql);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }
        
        // إنشاء ملف التكوين
        $configContent = "<?php
/**
 * إعدادات قاعدة البيانات
 * تم إنشاؤه تلقائياً بواسطة سكريبت التثبيت
 */

class Database {
    private \$host = '{$config['host']}';
    private \$db_name = '{$config['name']}';
    private \$username = '{$config['username']}';
    private \$password = '{$config['password']}';
    private \$charset = 'utf8mb4';
    private \$conn;

    public function connect() {
        \$this->conn = null;
        
        try {
            \$dsn = \"mysql:host=\" . \$this->host . \";dbname=\" . \$this->db_name . \";charset=\" . \$this->charset;
            \$options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            
            \$this->conn = new PDO(\$dsn, \$this->username, \$this->password, \$options);
        } catch(PDOException \$e) {
            error_log(\"Connection Error: \" . \$e->getMessage());
            throw new Exception(\"فشل الاتصال بقاعدة البيانات\");
        }
        
        return \$this->conn;
    }

    public function disconnect() {
        \$this->conn = null;
    }

    public function getConnection() {
        if (\$this->conn === null) {
            return \$this->connect();
        }
        return \$this->conn;
    }

    public function query(\$sql, \$params = []) {
        try {
            \$stmt = \$this->getConnection()->prepare(\$sql);
            \$stmt->execute(\$params);
            return \$stmt;
        } catch(PDOException \$e) {
            error_log(\"Query Error: \" . \$e->getMessage());
            throw new Exception(\"خطأ في تنفيذ الاستعلام\");
        }
    }

    public function fetchOne(\$sql, \$params = []) {
        \$stmt = \$this->query(\$sql, \$params);
        return \$stmt->fetch();
    }

    public function fetchAll(\$sql, \$params = []) {
        \$stmt = \$this->query(\$sql, \$params);
        return \$stmt->fetchAll();
    }

    public function insert(\$sql, \$params = []) {
        \$this->query(\$sql, \$params);
        return \$this->getConnection()->lastInsertId();
    }

    public function update(\$sql, \$params = []) {
        \$stmt = \$this->query(\$sql, \$params);
        return \$stmt->rowCount();
    }

    public function delete(\$sql, \$params = []) {
        \$stmt = \$this->query(\$sql, \$params);
        return \$stmt->rowCount();
    }

    public function beginTransaction() {
        return \$this->getConnection()->beginTransaction();
    }

    public function commit() {
        return \$this->getConnection()->commit();
    }

    public function rollback() {
        return \$this->getConnection()->rollback();
    }
}

class DB {
    private static \$instance = null;

    public static function getInstance() {
        if (self::\$instance === null) {
            self::\$instance = new Database();
        }
        return self::\$instance;
    }

    public static function connection() {
        return self::getInstance()->getConnection();
    }

    public static function query(\$sql, \$params = []) {
        return self::getInstance()->query(\$sql, \$params);
    }

    public static function fetchOne(\$sql, \$params = []) {
        return self::getInstance()->fetchOne(\$sql, \$params);
    }

    public static function fetchAll(\$sql, \$params = []) {
        return self::getInstance()->fetchAll(\$sql, \$params);
    }

    public static function insert(\$sql, \$params = []) {
        return self::getInstance()->insert(\$sql, \$params);
    }

    public static function update(\$sql, \$params = []) {
        return self::getInstance()->update(\$sql, \$params);
    }

    public static function delete(\$sql, \$params = []) {
        return self::getInstance()->delete(\$sql, \$params);
    }
}
?>";
        
        if (!is_dir('config')) {
            mkdir('config', 0777, true);
        }
        
        file_put_contents('config/database.php', $configContent);
        
        return ['success' => true];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'خطأ في إعداد قاعدة البيانات: ' . $e->getMessage()
        ];
    }
}

/**
 * إعداد المدير
 */
function setupAdmin($data) {
    try {
        require_once 'config/database.php';
        
        $db = DB::getInstance();
        
        // تشفير كلمة المرور
        $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
        
        // تحديث بيانات المدير الافتراضي
        $sql = "UPDATE users SET username = ?, email = ?, password = ?, full_name = ? WHERE id = 1";
        $db->update($sql, [
            $data['username'],
            $data['email'],
            $hashedPassword,
            $data['full_name']
        ]);
        
        return ['success' => true];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'خطأ في إعداد المدير: ' . $e->getMessage()
        ];
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت نظام إدارة عروض الأسعار</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .install-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 600px;
            width: 100%;
            margin: 20px;
        }
        
        .install-header {
            background: #2c3e50;
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .install-body {
            padding: 2rem;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: 600;
            position: relative;
        }
        
        .step.active {
            background: #3498db;
            color: white;
        }
        
        .step.completed {
            background: #27ae60;
            color: white;
        }
        
        .step::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -20px;
            width: 20px;
            height: 2px;
            background: #e9ecef;
            transform: translateY(-50%);
        }
        
        .step:last-child::after {
            display: none;
        }
        
        .step.completed::after {
            background: #27ae60;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 0.75rem;
        }
        
        .form-control:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
        
        .btn {
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
        }
        
        .requirement-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .requirement-ok {
            background: #d4edda;
            color: #155724;
        }
        
        .requirement-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .requirement-warning {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <h2><i class="fas fa-cog me-2"></i>تثبيت نظام إدارة عروض الأسعار</h2>
            <p>مرحباً بك في معالج التثبيت</p>
        </div>
        
        <div class="install-body">
            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step <?php echo $step >= 1 ? ($step > 1 ? 'completed' : 'active') : ''; ?>">1</div>
                <div class="step <?php echo $step >= 2 ? ($step > 2 ? 'completed' : 'active') : ''; ?>">2</div>
                <div class="step <?php echo $step >= 3 ? 'active' : ''; ?>">3</div>
            </div>
            
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?>" role="alert">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger" role="alert">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="text-center">
                    <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                    <h3 class="mt-3">تم التثبيت بنجاح!</h3>
                    <p class="text-muted">يمكنك الآن البدء في استخدام النظام</p>
                    <a href="login.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                    </a>
                </div>
            <?php elseif ($step == 1): ?>
                <!-- Step 1: Requirements Check -->
                <h4 class="mb-4">الخطوة 1: التحقق من المتطلبات</h4>
                
                <?php $requirements = checkRequirements(); ?>
                
                <div class="requirement-item requirement-ok">
                    <span>إصدار PHP (<?php echo PHP_VERSION; ?>)</span>
                    <i class="fas fa-check"></i>
                </div>
                
                <?php
                $requiredExtensions = ['pdo', 'pdo_mysql', 'gd', 'curl', 'mbstring', 'json'];
                foreach ($requiredExtensions as $ext):
                ?>
                <div class="requirement-item <?php echo extension_loaded($ext) ? 'requirement-ok' : 'requirement-error'; ?>">
                    <span>امتداد <?php echo $ext; ?></span>
                    <i class="fas <?php echo extension_loaded($ext) ? 'fa-check' : 'fa-times'; ?>"></i>
                </div>
                <?php endforeach; ?>
                
                <?php
                $writableDirs = ['uploads', 'assets/images', 'exports'];
                foreach ($writableDirs as $dir):
                    $writable = is_dir($dir) && is_writable($dir);
                ?>
                <div class="requirement-item <?php echo $writable ? 'requirement-ok' : 'requirement-warning'; ?>">
                    <span>صلاحية الكتابة: <?php echo $dir; ?></span>
                    <i class="fas <?php echo $writable ? 'fa-check' : 'fa-exclamation-triangle'; ?>"></i>
                </div>
                <?php endforeach; ?>
                
                <?php if (empty($requirements['errors'])): ?>
                <form method="POST" class="mt-4">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-arrow-left me-2"></i>التالي
                    </button>
                </form>
                <?php else: ?>
                <div class="alert alert-danger mt-4">
                    <strong>لا يمكن المتابعة!</strong><br>
                    يرجى حل المشاكل المذكورة أعلاه أولاً.
                </div>
                <?php endif; ?>
                
            <?php elseif ($step == 2): ?>
                <!-- Step 2: Database Setup -->
                <h4 class="mb-4">الخطوة 2: إعداد قاعدة البيانات</h4>
                
                <form method="POST">
                    <div class="mb-3">
                        <label for="db_host" class="form-label">خادم قاعدة البيانات</label>
                        <input type="text" class="form-control" id="db_host" name="db_host" value="localhost" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="db_name" class="form-label">اسم قاعدة البيانات</label>
                        <input type="text" class="form-control" id="db_name" name="db_name" value="quote_management_system" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="db_username" class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" id="db_username" name="db_username" value="root" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="db_password" class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" id="db_password" name="db_password">
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-database me-2"></i>إنشاء قاعدة البيانات
                    </button>
                </form>
                
            <?php elseif ($step == 3): ?>
                <!-- Step 3: Admin Setup -->
                <h4 class="mb-4">الخطوة 3: إعداد المدير</h4>
                
                <form method="POST">
                    <div class="mb-3">
                        <label for="admin_name" class="form-label">الاسم الكامل</label>
                        <input type="text" class="form-control" id="admin_name" name="admin_name" value="مدير النظام" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="admin_username" class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" id="admin_username" name="admin_username" value="admin" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="admin_email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="admin_email" name="admin_email" value="<EMAIL>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="admin_password" class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" id="admin_password" name="admin_password" required>
                        <div class="form-text">يجب أن تكون كلمة المرور قوية (8 أحرف على الأقل)</div>
                    </div>
                    
                    <button type="submit" class="btn btn-success w-100">
                        <i class="fas fa-check me-2"></i>إنهاء التثبيت
                    </button>
                </form>
            <?php endif; ?>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
