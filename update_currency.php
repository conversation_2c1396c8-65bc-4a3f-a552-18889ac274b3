<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث العملة للدينار الأردني</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            direction: rtl;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 100%;
            max-width: 600px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .message {
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            text-align: center;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #b3d9ff;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 10px;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background: #218838;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .currency-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🇯🇴 تحديث العملة للدينار الأردني</h1>
        
        <?php
        $message = '';
        $messageType = '';
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_currency'])) {
            try {
                if (!file_exists('config/database.php')) {
                    throw new Exception('ملف قاعدة البيانات غير موجود');
                }
                
                require_once 'config/database.php';
                $db = new Database();
                $conn = $db->connect();
                
                // التحقق من وجود جدول الإعدادات
                $stmt = $conn->prepare("SHOW TABLES LIKE 'settings'");
                $stmt->execute();
                
                if ($stmt->rowCount() == 0) {
                    // إنشاء جدول الإعدادات
                    $createSettings = "CREATE TABLE settings (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        setting_key VARCHAR(100) UNIQUE NOT NULL,
                        setting_value TEXT,
                        setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
                        description TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    )";
                    $conn->exec($createSettings);
                }
                
                // إعدادات العملة الأردنية
                $jordanianSettings = [
                    ['currency_code', 'JOD', 'string', 'رمز العملة'],
                    ['currency_symbol', 'د.أ', 'string', 'رمز العملة للعرض'],
                    ['currency_name', 'دينار أردني', 'string', 'اسم العملة'],
                    ['tax_rate', '16', 'number', 'نسبة ضريبة المبيعات الأردنية'],
                    ['company_name', 'شركة عروض الأسعار الأردنية', 'string', 'اسم الشركة'],
                    ['company_address', 'عمان، الأردن', 'string', 'عنوان الشركة'],
                    ['company_phone', '+962-6-XXXXXXX', 'string', 'هاتف الشركة'],
                    ['company_email', '<EMAIL>', 'string', 'بريد الشركة'],
                    ['decimal_places', '3', 'number', 'عدد الخانات العشرية للدينار'],
                    ['date_format', 'd/m/Y', 'string', 'تنسيق التاريخ'],
                    ['timezone', 'Asia/Amman', 'string', 'المنطقة الزمنية']
                ];
                
                // إدراج أو تحديث الإعدادات
                foreach ($jordanianSettings as $setting) {
                    $stmt = $conn->prepare("INSERT INTO settings (setting_key, setting_value, setting_type, description) 
                                          VALUES (?, ?, ?, ?) 
                                          ON DUPLICATE KEY UPDATE 
                                          setting_value = VALUES(setting_value),
                                          setting_type = VALUES(setting_type),
                                          description = VALUES(description)");
                    $stmt->execute($setting);
                }
                
                $message = '✅ تم تحديث إعدادات العملة للدينار الأردني بنجاح!';
                $messageType = 'success';
                
            } catch (Exception $e) {
                $message = '❌ خطأ: ' . $e->getMessage();
                $messageType = 'error';
            }
        }
        ?>
        
        <div class="info">
            <h3>🇯🇴 تحديث النظام للدينار الأردني</h3>
            <p>سيتم تحديث النظام ليستخدم الدينار الأردني كعملة أساسية مع الإعدادات التالية:</p>
            <ul>
                <li><strong>العملة:</strong> دينار أردني (JOD)</li>
                <li><strong>الرمز:</strong> د.أ</li>
                <li><strong>ضريبة المبيعات:</strong> 16%</li>
                <li><strong>الخانات العشرية:</strong> 3 خانات</li>
                <li><strong>المنطقة الزمنية:</strong> عمان، الأردن</li>
            </ul>
        </div>
        
        <?php if ($message): ?>
            <div class="message <?php echo $messageType; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <div class="currency-info">
            <h4>📊 معلومات الدينار الأردني:</h4>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 15px;">
                <div>
                    <strong>الرمز الدولي:</strong> JOD<br>
                    <strong>الرمز المحلي:</strong> د.أ<br>
                    <strong>التقسيم:</strong> 1000 فلس
                </div>
                <div>
                    <strong>ضريبة المبيعات:</strong> 16%<br>
                    <strong>الخانات العشرية:</strong> 3<br>
                    <strong>المنطقة الزمنية:</strong> GMT+3
                </div>
            </div>
        </div>
        
        <?php if ($messageType !== 'success'): ?>
        <form method="POST" style="text-align: center;">
            <button type="submit" name="update_currency" class="btn">
                🇯🇴 تحديث للدينار الأردني
            </button>
        </form>
        <?php endif; ?>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="start.html" class="btn btn-secondary">🏠 العودة للصفحة الرئيسية</a>
            <?php if ($messageType === 'success'): ?>
                <a href="simple_dashboard.php" class="btn">📊 الذهاب للوحة التحكم</a>
            <?php endif; ?>
        </div>
        
        <?php if ($messageType === 'success'): ?>
        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <h4>✅ تم التحديث بنجاح!</h4>
            <p>الآن يمكنك:</p>
            <ul>
                <li>إضافة عملاء جدد</li>
                <li>إنشاء عروض أسعار بالدينار الأردني</li>
                <li>استخدام ضريبة المبيعات الأردنية (16%)</li>
                <li>عرض الأسعار بـ 3 خانات عشرية</li>
            </ul>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
