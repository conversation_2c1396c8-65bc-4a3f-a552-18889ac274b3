<?php
/**
 * صفحة إنشاء عرض أسعار جديد
 * نظام إدارة عروض الأسعار الاحترافي
 */

// التحقق من وجود ملف التكوين أولاً
if (!file_exists('config/database.php')) {
    header('Location: simple_setup.php');
    exit;
}

try {
    require_once 'classes/Auth.php';
    require_once 'classes/Quote.php';
    require_once 'classes/Customer.php';
    require_once 'classes/Product.php';
    require_once 'classes/Settings.php';

    $auth = new Auth();
    $auth->requireLogin();
    $auth->requirePermission('create_quotes');

    $user = $auth->getCurrentUser();
    $quote = new Quote();
    $customer = new Customer();
    $product = new Product();
    $settings = new Settings();
} catch (Exception $e) {
    header('Location: simple_setup.php?error=classes_failed');
    exit;
}

$message = '';
$messageType = '';
$customers = $customer->getAll();
$products = $product->getAll();
$systemSettings = $settings->getSystemSettings();

// معالجة إنشاء العرض
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_quote'])) {
    $quoteData = [
        'customer_id' => $_POST['customer_id'],
        'quote_date' => $_POST['quote_date'],
        'valid_until' => $_POST['valid_until'],
        'tax_rate' => $_POST['tax_rate'],
        'discount_rate' => $_POST['discount_rate'] ?? 0,
        'notes' => $_POST['notes'] ?? '',
        'terms_conditions' => $_POST['terms_conditions'] ?? '',
        'created_by' => $user['id']
    ];
    
    $items = [];
    if (isset($_POST['items']) && is_array($_POST['items'])) {
        foreach ($_POST['items'] as $item) {
            if (!empty($item['description']) && !empty($item['quantity']) && !empty($item['unit_price'])) {
                $items[] = [
                    'item_description' => $item['description'],
                    'quantity' => floatval($item['quantity']),
                    'unit_price' => floatval($item['unit_price']),
                    'product_id' => !empty($item['product_id']) ? $item['product_id'] : null
                ];
            }
        }
    }
    
    if (empty($items)) {
        $message = 'يجب إضافة بند واحد على الأقل للعرض';
        $messageType = 'danger';
    } else {
        $result = $quote->create($quoteData, $items);
        if ($result['success']) {
            header('Location: quote_view.php?id=' . $result['quote_id'] . '&success=created');
            exit;
        } else {
            $message = $result['message'];
            $messageType = 'danger';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء عرض أسعار جديد - نظام إدارة عروض الأسعار</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        .item-row {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .item-row:hover {
            background: #e9ecef;
        }
        
        .remove-item {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .totals-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
        }
        
        .final-total {
            border-top: 2px solid var(--primary-color);
            padding-top: 15px;
            margin-top: 15px;
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4>نظام العروض</h4>
        </div>
        <ul class="sidebar-menu">
            <li><a href="simple_dashboard.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
            <li><a href="quotes.php" class="active"><i class="fas fa-file-invoice-dollar"></i> عروض الأسعار</a></li>
            <li><a href="customers.php"><i class="fas fa-users"></i> العملاء</a></li>
            <li><a href="products.php"><i class="fas fa-box"></i> المنتجات</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar"></i> التقارير</a></li>
            <?php if ($user['role'] === 'admin'): ?>
            <li><a href="users.php"><i class="fas fa-user-cog"></i> المستخدمين</a></li>
            <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
            <?php endif; ?>
            <li><a href="simple_logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar">
            <div class="d-flex align-items-center">
                <a href="quotes.php" class="btn btn-outline-secondary me-3">
                    <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                </a>
                <h5 class="mb-0">إنشاء عرض أسعار جديد</h5>
            </div>
            <div class="d-flex align-items-center">
                <span class="me-3">مرحباً، <?php echo htmlspecialchars($user['full_name']); ?></span>
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>الملف الشخصي</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="simple_logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="container-fluid p-4">
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <form method="POST" id="quoteForm">
                <div class="row">
                    <!-- معلومات العرض الأساسية -->
                    <div class="col-lg-8">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-info-circle me-2"></i>معلومات العرض الأساسية</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">العميل *</label>
                                        <select name="customer_id" class="form-select" required>
                                            <option value="">اختر العميل</option>
                                            <?php foreach ($customers as $cust): ?>
                                            <option value="<?php echo $cust['id']; ?>">
                                                <?php echo htmlspecialchars($cust['name']); ?>
                                                <?php if ($cust['company_name']): ?>
                                                    - <?php echo htmlspecialchars($cust['company_name']); ?>
                                                <?php endif; ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ العرض *</label>
                                        <input type="date" name="quote_date" class="form-control" value="<?php echo date('Y-m-d'); ?>" required>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">صالح حتى *</label>
                                        <input type="date" name="valid_until" class="form-control" value="<?php echo date('Y-m-d', strtotime('+30 days')); ?>" required>
                                    </div>
                                    
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">نسبة الضريبة (%)</label>
                                        <input type="number" name="tax_rate" class="form-control" value="<?php echo $systemSettings['tax_rate']; ?>" step="0.01" min="0" max="100">
                                    </div>
                                    
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">نسبة الخصم (%)</label>
                                        <input type="number" name="discount_rate" class="form-control" value="0" step="0.01" min="0" max="100">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- بنود العرض -->
                        <div class="card mb-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5><i class="fas fa-list me-2"></i>بنود العرض</h5>
                                <button type="button" class="btn btn-primary btn-sm" onclick="addItem()">
                                    <i class="fas fa-plus me-2"></i>إضافة بند
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="items-container">
                                    <!-- سيتم إضافة البنود هنا بواسطة JavaScript -->
                                </div>
                            </div>
                        </div>

                        <!-- ملاحظات وشروط -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-sticky-note me-2"></i>ملاحظات وشروط</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">ملاحظات</label>
                                        <textarea name="notes" class="form-control" rows="4" placeholder="أي ملاحظات إضافية..."></textarea>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الشروط والأحكام</label>
                                        <textarea name="terms_conditions" class="form-control" rows="4" placeholder="الشروط والأحكام..."></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ملخص العرض -->
                    <div class="col-lg-4">
                        <div class="card sticky-top" style="top: 20px;">
                            <div class="card-header">
                                <h5><i class="fas fa-calculator me-2"></i>ملخص العرض</h5>
                            </div>
                            <div class="card-body">
                                <div class="totals-section">
                                    <div class="total-row">
                                        <span>المجموع الفرعي:</span>
                                        <span id="subtotal">0.00 <?php echo htmlspecialchars($systemSettings['currency_symbol']); ?></span>
                                    </div>
                                    
                                    <div class="total-row">
                                        <span>الخصم:</span>
                                        <span id="discount">0.00 <?php echo htmlspecialchars($systemSettings['currency_symbol']); ?></span>
                                    </div>
                                    
                                    <div class="total-row">
                                        <span>الضريبة:</span>
                                        <span id="tax">0.00 <?php echo htmlspecialchars($systemSettings['currency_symbol']); ?></span>
                                    </div>
                                    
                                    <div class="total-row final-total">
                                        <span>الإجمالي النهائي:</span>
                                        <span id="total">0.00 <?php echo htmlspecialchars($systemSettings['currency_symbol']); ?></span>
                                    </div>
                                </div>
                                
                                <div class="d-grid gap-2 mt-3">
                                    <button type="submit" name="create_quote" class="btn btn-success btn-lg">
                                        <i class="fas fa-save me-2"></i>حفظ العرض
                                    </button>
                                    
                                    <button type="button" class="btn btn-outline-secondary" onclick="previewQuote()">
                                        <i class="fas fa-eye me-2"></i>معاينة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let itemCounter = 0;
        const products = <?php echo json_encode($products); ?>;
        const currencySymbol = '<?php echo htmlspecialchars($systemSettings['currency_symbol']); ?>';
        
        // إضافة بند جديد
        function addItem() {
            itemCounter++;
            const container = document.getElementById('items-container');
            const itemHtml = `
                <div class="item-row" id="item-${itemCounter}">
                    <div class="row align-items-end">
                        <div class="col-md-4 mb-2">
                            <label class="form-label">المنتج (اختياري)</label>
                            <select name="items[${itemCounter}][product_id]" class="form-select" onchange="selectProduct(this, ${itemCounter})">
                                <option value="">اختر منتج أو أدخل وصف مخصص</option>
                                ${products.map(product => `<option value="${product.id}" data-price="${product.unit_price}">${product.product_name} - ${product.part_number}</option>`).join('')}
                            </select>
                        </div>
                        
                        <div class="col-md-3 mb-2">
                            <label class="form-label">الوصف *</label>
                            <input type="text" name="items[${itemCounter}][description]" class="form-control" required placeholder="وصف البند">
                        </div>
                        
                        <div class="col-md-2 mb-2">
                            <label class="form-label">الكمية *</label>
                            <input type="number" name="items[${itemCounter}][quantity]" class="form-control" step="0.01" min="0.01" value="1" onchange="calculateTotals()" required>
                        </div>
                        
                        <div class="col-md-2 mb-2">
                            <label class="form-label">سعر الوحدة *</label>
                            <input type="number" name="items[${itemCounter}][unit_price]" class="form-control" step="0.01" min="0.01" onchange="calculateTotals()" required>
                        </div>
                        
                        <div class="col-md-1 mb-2">
                            <button type="button" class="remove-item" onclick="removeItem(${itemCounter})" title="حذف البند">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
            container.insertAdjacentHTML('beforeend', itemHtml);
        }
        
        // حذف بند
        function removeItem(id) {
            document.getElementById(`item-${id}`).remove();
            calculateTotals();
        }
        
        // اختيار منتج
        function selectProduct(select, itemId) {
            const option = select.options[select.selectedIndex];
            if (option.value) {
                const price = option.getAttribute('data-price');
                const description = option.text;
                
                document.querySelector(`#item-${itemId} input[name*="[description]"]`).value = description;
                document.querySelector(`#item-${itemId} input[name*="[unit_price]"]`).value = price;
                calculateTotals();
            }
        }
        
        // حساب الإجماليات
        function calculateTotals() {
            let subtotal = 0;
            
            // حساب المجموع الفرعي
            document.querySelectorAll('.item-row').forEach(row => {
                const quantity = parseFloat(row.querySelector('input[name*="[quantity]"]').value) || 0;
                const price = parseFloat(row.querySelector('input[name*="[unit_price]"]').value) || 0;
                subtotal += quantity * price;
            });
            
            // حساب الخصم
            const discountRate = parseFloat(document.querySelector('input[name="discount_rate"]').value) || 0;
            const discountAmount = subtotal * (discountRate / 100);
            
            // حساب الضريبة
            const taxRate = parseFloat(document.querySelector('input[name="tax_rate"]').value) || 0;
            const taxableAmount = subtotal - discountAmount;
            const taxAmount = taxableAmount * (taxRate / 100);
            
            // الإجمالي النهائي
            const total = taxableAmount + taxAmount;
            
            // تحديث العرض
            document.getElementById('subtotal').textContent = subtotal.toFixed(2) + ' ' + currencySymbol;
            document.getElementById('discount').textContent = discountAmount.toFixed(2) + ' ' + currencySymbol;
            document.getElementById('tax').textContent = taxAmount.toFixed(2) + ' ' + currencySymbol;
            document.getElementById('total').textContent = total.toFixed(2) + ' ' + currencySymbol;
        }
        
        // معاينة العرض
        function previewQuote() {
            alert('ميزة المعاينة ستكون متاحة قريباً');
        }
        
        // إضافة بند افتراضي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addItem();
            
            // ربط أحداث تغيير الضريبة والخصم
            document.querySelector('input[name="tax_rate"]').addEventListener('change', calculateTotals);
            document.querySelector('input[name="discount_rate"]').addEventListener('change', calculateTotals);
        });
    </script>
</body>
</html>
