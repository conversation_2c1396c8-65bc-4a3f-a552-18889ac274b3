# دليل حل المشاكل - نظام إدارة عروض الأسعار

## 🚨 المشكلة: Internal Server Error

إذا واجهت خطأ "Internal Server Error" عند الوصول للنظام، اتبع هذه الخطوات:

### 🔍 الخطوة 1: تشخيص المشكلة
انتقل إلى: `http://localhost/your_project_folder/debug.php`

هذا الملف سيعرض لك:
- حالة PHP والامتدادات المطلوبة
- حالة الملفات والمجلدات
- حالة قاعدة البيانات
- توصيات لحل المشاكل

### ⚡ الخطوة 2: الإعداد السريع
انتقل إلى: `http://localhost/your_project_folder/quick_setup.php`

هذا الملف سيقوم بـ:
- إنشاء قاعدة البيانات تلقائياً
- إنشاء الجداول الأساسية
- إنشاء ملف التكوين
- إضافة المستخدم الافتراضي

### 🏠 الخطوة 3: صفحة الترحيب
انتقل إلى: `http://localhost/your_project_folder/welcome.php`

هذه الصفحة تعرض:
- حالة النظام
- الميزات المتاحة
- روابط سريعة للإعداد
- بيانات الدخول الافتراضية

## 🛠️ الحلول الشائعة

### المشكلة: ملف config/database.php غير موجود
**الحل:**
1. انتقل إلى `quick_setup.php`
2. أدخل بيانات قاعدة البيانات
3. اضغط "إعداد النظام"

### المشكلة: خطأ في الاتصال بقاعدة البيانات
**الحل:**
1. تأكد من تشغيل MySQL في XAMPP
2. تحقق من بيانات الاتصال:
   - الخادم: localhost
   - المستخدم: root
   - كلمة المرور: (فارغة عادة)
   - قاعدة البيانات: quote_management_system

### المشكلة: صلاحيات المجلدات
**الحل:**
```bash
chmod 755 uploads/
chmod 755 assets/images/
chmod 755 config/
```

### المشكلة: امتدادات PHP مفقودة
**الحل:**
تأكد من تفعيل هذه الامتدادات في `php.ini`:
- pdo
- pdo_mysql
- gd
- curl
- mbstring
- json

## 📋 خطوات التشغيل الصحيحة

### 1. تشغيل XAMPP
- شغل Apache
- شغل MySQL

### 2. إعداد النظام
- انتقل إلى `http://localhost/your_project_folder`
- ستتم إعادة توجيهك لصفحة الترحيب
- اختر "إعداد سريع" أو "معالج التثبيت"

### 3. تسجيل الدخول
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

## 🔗 الروابط المفيدة

- **الصفحة الرئيسية**: `http://localhost/your_project_folder/`
- **صفحة الترحيب**: `http://localhost/your_project_folder/welcome.php`
- **تشخيص المشاكل**: `http://localhost/your_project_folder/debug.php`
- **الإعداد السريع**: `http://localhost/your_project_folder/quick_setup.php`
- **معالج التثبيت**: `http://localhost/your_project_folder/install.php`
- **تسجيل الدخول**: `http://localhost/your_project_folder/login.php`

## 📞 الدعم الإضافي

إذا استمرت المشاكل:

1. **تحقق من سجل أخطاء Apache**:
   - في XAMPP: `xampp/apache/logs/error.log`

2. **تحقق من سجل أخطاء PHP**:
   - في XAMPP: `xampp/php/logs/php_error_log`

3. **إعادة تشغيل XAMPP**:
   - أوقف Apache و MySQL
   - شغلهما مرة أخرى

4. **تحقق من المنافذ**:
   - Apache: المنفذ 80
   - MySQL: المنفذ 3306

## ✅ التحقق من نجاح الإعداد

النظام يعمل بشكل صحيح عندما:
- ✅ تظهر صفحة الترحيب بدون أخطاء
- ✅ يمكن تسجيل الدخول بـ admin/admin123
- ✅ تظهر لوحة التحكم مع الإحصائيات
- ✅ يمكن الوصول لصفحات العملاء والمنتجات

---

**نصيحة**: احتفظ بهذا الدليل مرجعاً سريعاً لحل أي مشاكل مستقبلية!
