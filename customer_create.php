<?php
/**
 * صفحة إضافة عميل جديد
 * نظام إدارة عروض الأسعار الاحترافي
 */

// التحقق من وجود ملف التكوين أولاً
if (!file_exists('config/database.php')) {
    header('Location: simple_setup.php');
    exit;
}

try {
    require_once 'classes/Auth.php';
    require_once 'classes/Customer.php';

    $auth = new Auth();
    $auth->requireLogin();
    $auth->requirePermission('create_customers');

    $user = $auth->getCurrentUser();
    $customer = new Customer();
} catch (Exception $e) {
    header('Location: simple_setup.php?error=classes_failed');
    exit;
}

$message = '';
$messageType = '';

// معالجة إضافة العميل
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_customer'])) {
    $customerData = [
        'name' => trim($_POST['name']),
        'company_name' => trim($_POST['company_name']),
        'email' => trim($_POST['email']),
        'phone' => trim($_POST['phone']),
        'address' => trim($_POST['address']),
        'city' => trim($_POST['city']),
        'postal_code' => trim($_POST['postal_code']),
        'tax_number' => trim($_POST['tax_number']),
        'notes' => trim($_POST['notes'])
    ];
    
    $result = $customer->create($customerData);
    if ($result['success']) {
        header('Location: customers.php?success=created');
        exit;
    } else {
        $message = $result['message'];
        $messageType = 'danger';
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة عميل جديد - نظام إدارة عروض الأسعار</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4>نظام العروض</h4>
        </div>
        <ul class="sidebar-menu">
            <li><a href="simple_dashboard.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
            <li><a href="quotes.php"><i class="fas fa-file-invoice-dollar"></i> عروض الأسعار</a></li>
            <li><a href="customers.php" class="active"><i class="fas fa-users"></i> العملاء</a></li>
            <li><a href="products.php"><i class="fas fa-box"></i> المنتجات</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar"></i> التقارير</a></li>
            <?php if ($user['role'] === 'admin'): ?>
            <li><a href="users.php"><i class="fas fa-user-cog"></i> المستخدمين</a></li>
            <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
            <?php endif; ?>
            <li><a href="simple_logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar">
            <div class="d-flex align-items-center">
                <a href="customers.php" class="btn btn-outline-secondary me-3">
                    <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                </a>
                <h5 class="mb-0">إضافة عميل جديد</h5>
            </div>
            <div class="d-flex align-items-center">
                <span class="me-3">مرحباً، <?php echo htmlspecialchars($user['full_name']); ?></span>
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>الملف الشخصي</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="simple_logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="container-fluid p-4">
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-user-plus me-2"></i>بيانات العميل الجديد</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <div class="row">
                                    <!-- المعلومات الأساسية -->
                                    <div class="col-12">
                                        <h6 class="text-primary mb-3">المعلومات الأساسية</h6>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">اسم العميل *</label>
                                        <input type="text" name="name" class="form-control" 
                                               value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>" 
                                               required placeholder="الاسم الكامل للعميل">
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">اسم الشركة</label>
                                        <input type="text" name="company_name" class="form-control" 
                                               value="<?php echo htmlspecialchars($_POST['company_name'] ?? ''); ?>" 
                                               placeholder="اسم الشركة (اختياري)">
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" name="email" class="form-control" 
                                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                                               placeholder="<EMAIL>">
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" name="phone" class="form-control" 
                                               value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>" 
                                               placeholder="05xxxxxxxx">
                                    </div>
                                    
                                    <!-- معلومات العنوان -->
                                    <div class="col-12 mt-4">
                                        <h6 class="text-primary mb-3">معلومات العنوان</h6>
                                    </div>
                                    
                                    <div class="col-12 mb-3">
                                        <label class="form-label">العنوان</label>
                                        <textarea name="address" class="form-control" rows="3" 
                                                  placeholder="العنوان التفصيلي"><?php echo htmlspecialchars($_POST['address'] ?? ''); ?></textarea>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">المدينة</label>
                                        <input type="text" name="city" class="form-control" 
                                               value="<?php echo htmlspecialchars($_POST['city'] ?? ''); ?>" 
                                               placeholder="اسم المدينة">
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الرمز البريدي</label>
                                        <input type="text" name="postal_code" class="form-control" 
                                               value="<?php echo htmlspecialchars($_POST['postal_code'] ?? ''); ?>" 
                                               placeholder="12345">
                                    </div>
                                    
                                    <!-- معلومات إضافية -->
                                    <div class="col-12 mt-4">
                                        <h6 class="text-primary mb-3">معلومات إضافية</h6>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الرقم الضريبي</label>
                                        <input type="text" name="tax_number" class="form-control" 
                                               value="<?php echo htmlspecialchars($_POST['tax_number'] ?? ''); ?>" 
                                               placeholder="الرقم الضريبي للشركة">
                                    </div>
                                    
                                    <div class="col-12 mb-3">
                                        <label class="form-label">ملاحظات</label>
                                        <textarea name="notes" class="form-control" rows="3" 
                                                  placeholder="أي ملاحظات إضافية عن العميل"><?php echo htmlspecialchars($_POST['notes'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                                
                                <div class="d-flex justify-content-between mt-4">
                                    <a href="customers.php" class="btn btn-secondary">
                                        <i class="fas fa-times me-2"></i>إلغاء
                                    </a>
                                    
                                    <button type="submit" name="create_customer" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>حفظ العميل
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // التركيز على حقل الاسم
            document.querySelector('input[name="name"]').focus();
            
            // تنسيق رقم الهاتف
            const phoneInput = document.querySelector('input[name="phone"]');
            phoneInput.addEventListener('input', function() {
                let value = this.value.replace(/\D/g, '');
                if (value.length > 0 && !value.startsWith('05')) {
                    if (value.startsWith('5')) {
                        value = '0' + value;
                    }
                }
                this.value = value;
            });
            
            // تحويل البريد الإلكتروني إلى أحرف صغيرة
            const emailInput = document.querySelector('input[name="email"]');
            emailInput.addEventListener('blur', function() {
                this.value = this.value.toLowerCase();
            });
        });
    </script>
</body>
</html>
