# دليل البدء السريع - نظام إدارة عروض الأسعار الاحترافي

## 🚀 التثبيت السريع

### 1. متطلبات النظام
- **PHP 8.0+** مع الامتدادات: PDO, PDO_MySQL, GD, cURL, mbstring, JSON
- **MySQL 5.7+** أو MariaDB 10.2+
- **Apache/Nginx** مع mod_rewrite
- **مساحة تخزين**: 100 ميجابايت على الأقل

### 2. خطوات التثبيت

#### الطريقة الأولى: التثبيت التلقائي
1. ارفع جميع الملفات إلى مجلد الموقع
2. انتقل إلى: `http://your-domain.com/install.php`
3. اتبع خطوات المعالج

#### الطريقة الثانية: التثبيت اليدوي
1. إنشاء قاعدة بيانات جديدة
2. تشغيل سكريبت SQL:
```bash
mysql -u username -p database_name < database_setup.sql
```
3. تعديل إعدادات قاعدة البيانات في `config/database.php`
4. تعيين صلاحيات المجلدات:
```bash
chmod 755 uploads/ assets/images/ exports/
```

### 3. بيانات الدخول الافتراضية
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## 📋 الاستخدام السريع

### إنشاء عرض أسعار جديد
1. **إضافة عميل**:
   - انتقل إلى "العملاء" → "إضافة عميل جديد"
   - أدخل بيانات العميل (الاسم، الهاتف، البريد الإلكتروني، إلخ)

2. **إضافة منتجات**:
   - انتقل إلى "المنتجات" → "إضافة منتج جديد"
   - أدخل بيانات المنتج (الكود، الاسم، السعر، إلخ)

3. **إنشاء العرض**:
   - انتقل إلى "عروض الأسعار" → "إضافة عرض جديد"
   - اختر العميل
   - أضف بنود العرض
   - احفظ وقم بتوليد PDF

### إعداد النظام
1. **بيانات الشركة**:
   - انتقل إلى "الإعدادات" → "بيانات الشركة"
   - أدخل اسم الشركة، العنوان، الهاتف
   - ارفع شعار الشركة

2. **إعدادات الضرائب**:
   - حدد نسبة الضريبة الافتراضية
   - اختر العملة ورمزها

## 🔧 التخصيص السريع

### تغيير الألوان
عدل المتغيرات في `assets/css/style.css`:
```css
:root {
    --primary-color: #2c3e50;    /* اللون الأساسي */
    --secondary-color: #3498db;  /* اللون الثانوي */
    --success-color: #27ae60;    /* لون النجاح */
}
```

### إضافة حقول جديدة للعملاء
1. عدل جدول `customers` في قاعدة البيانات
2. حدث كلاس `Customer.php`
3. عدل نماذج العملاء في الواجهات

### تخصيص قالب PDF
عدل دالة `generateQuoteHTML()` في `classes/PDFGenerator.php`

## 📱 الميزات الرئيسية

### ✅ إدارة العملاء
- إضافة وتعديل وحذف العملاء
- البحث السريع
- تتبع تاريخ التعامل

### ✅ إدارة المنتجات
- كتالوج شامل للمنتجات
- تصنيف حسب الفئات
- رفع صور المنتجات

### ✅ عروض الأسعار
- إنشاء عروض احترافية
- حساب تلقائي للضرائب
- تتبع حالة العرض
- توليد PDF عالي الجودة

### ✅ التقارير
- إحصائيات شاملة
- تصدير إلى Excel
- لوحة تحكم تفاعلية

## 🛡️ الأمان

### الحماية المدمجة
- حماية من SQL Injection
- حماية من XSS
- تشفير كلمات المرور
- نظام صلاحيات متقدم

### نصائح أمنية
1. غير كلمة مرور المدير فوراً
2. استخدم HTTPS في الإنتاج
3. قم بعمل نسخ احتياطية دورية
4. حدث النظام بانتظام

## 🔄 النسخ الاحتياطي

### نسخ احتياطي لقاعدة البيانات
```bash
mysqldump -u username -p database_name > backup.sql
```

### نسخ احتياطي للملفات
```bash
tar -czf backup_files.tar.gz uploads/ assets/images/
```

## 📞 الدعم الفني

### المشاكل الشائعة

**مشكلة**: لا يمكن رفع الصور
**الحل**: تحقق من صلاحيات مجلد `uploads/`

**مشكلة**: خطأ في الاتصال بقاعدة البيانات
**الحل**: تحقق من إعدادات `config/database.php`

**مشكلة**: لا يظهر PDF بشكل صحيح
**الحل**: تأكد من تثبيت مكتبة TCPDF

### طلب المساعدة
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966 11 123 4567
- **الموقع**: www.company.com/support

## 🚀 التطوير المستقبلي

### الميزات القادمة
- [ ] تكامل WhatsApp Business API
- [ ] تطبيق موبايل
- [ ] نظام محاسبة متكامل
- [ ] دعم عدة لغات
- [ ] API للتكامل الخارجي

### المساهمة
نرحب بمساهماتكم في تطوير النظام:
1. Fork المشروع
2. إنشاء فرع جديد
3. إرسال Pull Request

---

**تم إنشاء هذا النظام بواسطة فريق التطوير المحترف**

للمزيد من المعلومات، راجع ملف `README.md` الكامل.
