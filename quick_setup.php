<?php
/**
 * إعداد سريع لقاعدة البيانات
 * نظام إدارة عروض الأسعار الاحترافي
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $host = $_POST['host'] ?? 'localhost';
    $username = $_POST['username'] ?? 'root';
    $password = $_POST['password'] ?? '';
    $database = $_POST['database'] ?? 'quote_management_system';
    
    try {
        // الاتصال بـ MySQL
        $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // إنشاء قاعدة البيانات
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `$database`");
        
        // إنشاء الجداول الأساسية
        $tables = [
            "CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                role ENUM('admin', 'employee') DEFAULT 'employee',
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
            
            "CREATE TABLE IF NOT EXISTS customers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                phone VARCHAR(20),
                email VARCHAR(100),
                address TEXT,
                company_name VARCHAR(100),
                tax_number VARCHAR(50),
                notes TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
            
            "CREATE TABLE IF NOT EXISTS products (
                id INT AUTO_INCREMENT PRIMARY KEY,
                part_number VARCHAR(50) UNIQUE NOT NULL,
                product_name VARCHAR(200) NOT NULL,
                description TEXT,
                product_image VARCHAR(255),
                unit_price DECIMAL(10,2) NOT NULL,
                unit_cost DECIMAL(10,2),
                category VARCHAR(100),
                unit_type VARCHAR(50) DEFAULT 'قطعة',
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",
            
            "CREATE TABLE IF NOT EXISTS settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(100) UNIQUE NOT NULL,
                setting_value TEXT,
                setting_type ENUM('text', 'number', 'boolean', 'json') DEFAULT 'text',
                description VARCHAR(255),
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )"
        ];
        
        foreach ($tables as $sql) {
            $pdo->exec($sql);
        }
        
        // إدراج المستخدم الافتراضي
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $pdo->exec("INSERT IGNORE INTO users (id, username, email, password, full_name, role) VALUES 
                   (1, 'admin', '<EMAIL>', '$hashedPassword', 'مدير النظام', 'admin')");
        
        // إدراج الإعدادات الافتراضية
        $settings = [
            "('company_name', 'شركة الأمثل للتجارة', 'text', 'اسم الشركة')",
            "('company_address', 'الرياض، المملكة العربية السعودية', 'text', 'عنوان الشركة')",
            "('company_phone', '+966 11 123 4567', 'text', 'هاتف الشركة')",
            "('company_email', '<EMAIL>', 'text', 'بريد الشركة')",
            "('tax_rate', '15', 'number', 'نسبة الضريبة')",
            "('currency', 'ريال سعودي', 'text', 'العملة')",
            "('currency_symbol', 'ر.س', 'text', 'رمز العملة')"
        ];
        
        foreach ($settings as $setting) {
            $pdo->exec("INSERT IGNORE INTO settings (setting_key, setting_value, setting_type, description) VALUES $setting");
        }
        
        // إنشاء ملف التكوين
        $configContent = "<?php
class Database {
    private \$host = '$host';
    private \$db_name = '$database';
    private \$username = '$username';
    private \$password = '$password';
    private \$charset = 'utf8mb4';
    private \$conn;

    public function connect() {
        \$this->conn = null;
        try {
            \$dsn = \"mysql:host=\" . \$this->host . \";dbname=\" . \$this->db_name . \";charset=\" . \$this->charset;
            \$options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            \$this->conn = new PDO(\$dsn, \$this->username, \$this->password, \$options);
        } catch(PDOException \$e) {
            error_log(\"Connection Error: \" . \$e->getMessage());
            throw new Exception(\"فشل الاتصال بقاعدة البيانات\");
        }
        return \$this->conn;
    }

    public function getConnection() {
        if (\$this->conn === null) {
            return \$this->connect();
        }
        return \$this->conn;
    }

    public function query(\$sql, \$params = []) {
        try {
            \$stmt = \$this->getConnection()->prepare(\$sql);
            \$stmt->execute(\$params);
            return \$stmt;
        } catch(PDOException \$e) {
            error_log(\"Query Error: \" . \$e->getMessage());
            throw new Exception(\"خطأ في تنفيذ الاستعلام\");
        }
    }

    public function fetchOne(\$sql, \$params = []) {
        \$stmt = \$this->query(\$sql, \$params);
        return \$stmt->fetch();
    }

    public function fetchAll(\$sql, \$params = []) {
        \$stmt = \$this->query(\$sql, \$params);
        return \$stmt->fetchAll();
    }

    public function insert(\$sql, \$params = []) {
        \$this->query(\$sql, \$params);
        return \$this->getConnection()->lastInsertId();
    }

    public function update(\$sql, \$params = []) {
        \$stmt = \$this->query(\$sql, \$params);
        return \$stmt->rowCount();
    }

    public function delete(\$sql, \$params = []) {
        \$stmt = \$this->query(\$sql, \$params);
        return \$stmt->rowCount();
    }
}

class DB {
    private static \$instance = null;

    public static function getInstance() {
        if (self::\$instance === null) {
            self::\$instance = new Database();
        }
        return self::\$instance;
    }

    public static function connection() {
        return self::getInstance()->getConnection();
    }

    public static function query(\$sql, \$params = []) {
        return self::getInstance()->query(\$sql, \$params);
    }

    public static function fetchOne(\$sql, \$params = []) {
        return self::getInstance()->fetchOne(\$sql, \$params);
    }

    public static function fetchAll(\$sql, \$params = []) {
        return self::getInstance()->fetchAll(\$sql, \$params);
    }

    public static function insert(\$sql, \$params = []) {
        return self::getInstance()->insert(\$sql, \$params);
    }

    public static function update(\$sql, \$params = []) {
        return self::getInstance()->update(\$sql, \$params);
    }

    public static function delete(\$sql, \$params = []) {
        return self::getInstance()->delete(\$sql, \$params);
    }
}
?>";
        
        if (!is_dir('config')) {
            mkdir('config', 0755, true);
        }
        
        file_put_contents('config/database.php', $configContent);
        
        $message = 'تم إعداد النظام بنجاح! يمكنك الآن تسجيل الدخول باستخدام: admin / admin123';
        $messageType = 'success';
        
    } catch (Exception $e) {
        $message = 'خطأ في الإعداد: ' . $e->getMessage();
        $messageType = 'error';
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد سريع - نظام إدارة عروض الأسعار</title>
    <style>
        * {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
        }
        
        .header {
            background: #2c3e50;
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .body {
            padding: 2rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }
        
        input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
        }
        
        input:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .btn {
            width: 100%;
            padding: 1rem;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            margin-top: 1rem;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .links {
            text-align: center;
            margin-top: 1rem;
        }
        
        .links a {
            color: #3498db;
            text-decoration: none;
            margin: 0 10px;
            padding: 5px 10px;
            border-radius: 5px;
            background: #e7f3ff;
        }
        
        .links a:hover {
            background: #3498db;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>إعداد سريع للنظام</h1>
            <p>نظام إدارة عروض الأسعار الاحترافي</p>
        </div>
        
        <div class="body">
            <?php if ($message): ?>
                <div class="message <?php echo $messageType; ?>">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($messageType !== 'success'): ?>
            <form method="POST">
                <div class="form-group">
                    <label for="host">خادم قاعدة البيانات:</label>
                    <input type="text" id="host" name="host" value="localhost" required>
                </div>
                
                <div class="form-group">
                    <label for="username">اسم المستخدم:</label>
                    <input type="text" id="username" name="username" value="root" required>
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور:</label>
                    <input type="password" id="password" name="password" placeholder="اتركه فارغاً إذا لم تكن هناك كلمة مرور">
                </div>
                
                <div class="form-group">
                    <label for="database">اسم قاعدة البيانات:</label>
                    <input type="text" id="database" name="database" value="quote_management_system" required>
                </div>
                
                <button type="submit" class="btn">إعداد النظام</button>
            </form>
            <?php endif; ?>
            
            <div class="links">
                <a href="debug.php">تشخيص المشاكل</a>
                <a href="login.php">تسجيل الدخول</a>
                <a href="install.php">معالج التثبيت</a>
            </div>
        </div>
    </div>
</body>
</html>
