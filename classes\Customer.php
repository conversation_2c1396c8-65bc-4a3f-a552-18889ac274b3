<?php
/**
 * كلاس إدارة العملاء
 * نظام إدارة عروض الأسعار الاحترافي
 */

class Customer {
    private $db;

    public function __construct() {
        $this->initDatabase();
    }

    /**
     * تهيئة قاعدة البيانات
     */
    private function initDatabase() {
        try {
            if (file_exists(__DIR__ . '/../config/database.php')) {
                require_once __DIR__ . '/../config/database.php';
                if (class_exists('DB')) {
                    $this->db = DB::getInstance();
                } elseif (class_exists('Database')) {
                    $this->db = new Database();
                } else {
                    throw new Exception('Database class not found');
                }
            } else {
                throw new Exception('Database config file not found');
            }
        } catch (Exception $e) {
            error_log("Database Init Error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إضافة عميل جديد
     */
    public function create($data) {
        try {
            $sql = "INSERT INTO customers (name, phone, email, address, company_name, tax_number, notes, created_by) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            
            $customerId = $this->db->insert($sql, [
                $data['name'],
                $data['phone'] ?? null,
                $data['email'] ?? null,
                $data['address'] ?? null,
                $data['company_name'] ?? null,
                $data['tax_number'] ?? null,
                $data['notes'] ?? null,
                $data['created_by']
            ]);
            
            return [
                'success' => true,
                'message' => 'تم إضافة العميل بنجاح',
                'customer_id' => $customerId
            ];
            
        } catch (Exception $e) {
            error_log("Create Customer Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء إضافة العميل'
            ];
        }
    }

    /**
     * تحديث بيانات العميل
     */
    public function update($id, $data) {
        try {
            $sql = "UPDATE customers SET 
                    name = ?, phone = ?, email = ?, address = ?, 
                    company_name = ?, tax_number = ?, notes = ?
                    WHERE id = ?";
            
            $affected = $this->db->update($sql, [
                $data['name'],
                $data['phone'] ?? null,
                $data['email'] ?? null,
                $data['address'] ?? null,
                $data['company_name'] ?? null,
                $data['tax_number'] ?? null,
                $data['notes'] ?? null,
                $id
            ]);
            
            if ($affected > 0) {
                return [
                    'success' => true,
                    'message' => 'تم تحديث بيانات العميل بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'لم يتم العثور على العميل'
                ];
            }
            
        } catch (Exception $e) {
            error_log("Update Customer Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث بيانات العميل'
            ];
        }
    }

    /**
     * حذف عميل (إلغاء تفعيل)
     */
    public function delete($id) {
        try {
            $sql = "UPDATE customers SET is_active = 0 WHERE id = ?";
            $affected = $this->db->update($sql, [$id]);
            
            if ($affected > 0) {
                return [
                    'success' => true,
                    'message' => 'تم حذف العميل بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'لم يتم العثور على العميل'
                ];
            }
            
        } catch (Exception $e) {
            error_log("Delete Customer Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف العميل'
            ];
        }
    }

    /**
     * الحصول على عميل بواسطة ID
     */
    public function getById($id) {
        try {
            $sql = "SELECT c.*, u.full_name as created_by_name 
                    FROM customers c 
                    LEFT JOIN users u ON c.created_by = u.id 
                    WHERE c.id = ? AND c.is_active = 1";
            
            return $this->db->fetchOne($sql, [$id]);
            
        } catch (Exception $e) {
            error_log("Get Customer Error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * الحصول على جميع العملاء
     */
    public function getAll($filters = []) {
        try {
            $sql = "SELECT c.*, u.full_name as created_by_name 
                    FROM customers c 
                    LEFT JOIN users u ON c.created_by = u.id 
                    WHERE c.is_active = 1";
            $params = [];
            
            // تطبيق الفلاتر
            if (!empty($filters['search'])) {
                $sql .= " AND (c.name LIKE ? OR c.phone LIKE ? OR c.email LIKE ? OR c.company_name LIKE ?)";
                $searchTerm = '%' . $filters['search'] . '%';
                $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
            }
            
            if (!empty($filters['company'])) {
                $sql .= " AND c.company_name LIKE ?";
                $params[] = '%' . $filters['company'] . '%';
            }
            
            // ترتيب النتائج
            $orderBy = $filters['order_by'] ?? 'name';
            $orderDir = $filters['order_dir'] ?? 'ASC';
            $sql .= " ORDER BY c.$orderBy $orderDir";
            
            // تحديد عدد النتائج
            if (!empty($filters['limit'])) {
                $sql .= " LIMIT " . intval($filters['limit']);
                if (!empty($filters['offset'])) {
                    $sql .= " OFFSET " . intval($filters['offset']);
                }
            }
            
            return $this->db->fetchAll($sql, $params);
            
        } catch (Exception $e) {
            error_log("Get All Customers Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * البحث عن العملاء
     */
    public function search($query) {
        try {
            $sql = "SELECT c.*, u.full_name as created_by_name 
                    FROM customers c 
                    LEFT JOIN users u ON c.created_by = u.id 
                    WHERE c.is_active = 1 
                    AND (c.name LIKE ? OR c.phone LIKE ? OR c.email LIKE ? OR c.company_name LIKE ?)
                    ORDER BY c.name ASC
                    LIMIT 20";
            
            $searchTerm = '%' . $query . '%';
            return $this->db->fetchAll($sql, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
            
        } catch (Exception $e) {
            error_log("Search Customers Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * عدد العملاء
     */
    public function getCount($filters = []) {
        try {
            $sql = "SELECT COUNT(*) as total FROM customers WHERE is_active = 1";
            $params = [];
            
            if (!empty($filters['search'])) {
                $sql .= " AND (name LIKE ? OR phone LIKE ? OR email LIKE ? OR company_name LIKE ?)";
                $searchTerm = '%' . $filters['search'] . '%';
                $params = [$searchTerm, $searchTerm, $searchTerm, $searchTerm];
            }
            
            $result = $this->db->fetchOne($sql, $params);
            return $result['total'];
            
        } catch (Exception $e) {
            error_log("Get Customer Count Error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * الحصول على عروض أسعار العميل
     */
    public function getQuotes($customerId, $limit = 10) {
        try {
            $sql = "SELECT q.*, u.full_name as created_by_name 
                    FROM quotes q 
                    LEFT JOIN users u ON q.created_by = u.id 
                    WHERE q.customer_id = ? 
                    ORDER BY q.created_at DESC 
                    LIMIT ?";
            
            return $this->db->fetchAll($sql, [$customerId, $limit]);
            
        } catch (Exception $e) {
            error_log("Get Customer Quotes Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * التحقق من صحة البيانات
     */
    public function validate($data, $isUpdate = false) {
        $errors = [];
        
        // التحقق من الاسم
        if (empty($data['name']) || strlen(trim($data['name'])) < 2) {
            $errors[] = 'اسم العميل مطلوب ويجب أن يكون أكثر من حرفين';
        }
        
        // التحقق من رقم الهاتف
        if (!empty($data['phone']) && !preg_match('/^[+]?[0-9\s\-\(\)]{10,20}$/', $data['phone'])) {
            $errors[] = 'رقم الهاتف غير صحيح';
        }
        
        // التحقق من البريد الإلكتروني
        if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'البريد الإلكتروني غير صحيح';
        }
        
        return $errors;
    }

    /**
     * تصدير العملاء إلى CSV
     */
    public function exportToCSV($filters = []) {
        try {
            $customers = $this->getAll($filters);
            
            $filename = 'customers_' . date('Y-m-d_H-i-s') . '.csv';
            $filepath = 'exports/' . $filename;
            
            // إنشاء مجلد التصدير إذا لم يكن موجوداً
            if (!is_dir('exports')) {
                mkdir('exports', 0777, true);
            }
            
            $file = fopen($filepath, 'w');
            
            // إضافة BOM للدعم العربي
            fwrite($file, "\xEF\xBB\xBF");
            
            // عناوين الأعمدة
            fputcsv($file, [
                'الاسم',
                'رقم الهاتف',
                'البريد الإلكتروني',
                'العنوان',
                'اسم الشركة',
                'الرقم الضريبي',
                'تاريخ الإنشاء'
            ]);
            
            // بيانات العملاء
            foreach ($customers as $customer) {
                fputcsv($file, [
                    $customer['name'],
                    $customer['phone'],
                    $customer['email'],
                    $customer['address'],
                    $customer['company_name'],
                    $customer['tax_number'],
                    $customer['created_at']
                ]);
            }
            
            fclose($file);
            
            return [
                'success' => true,
                'filename' => $filename,
                'filepath' => $filepath
            ];
            
        } catch (Exception $e) {
            error_log("Export Customers Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء تصدير البيانات'
            ];
        }
    }
}
