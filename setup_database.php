<?php
/**
 * سكريبت إعداد قاعدة البيانات المباشر
 * نظام إدارة عروض الأسعار الاحترافي
 */

// إعدادات قاعدة البيانات - عدل هذه القيم حسب إعداداتك
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'quote_management_system';

echo "=== سكريبت إعداد قاعدة البيانات ===\n";
echo "نظام إدارة عروض الأسعار الاحترافي\n\n";

try {
    // الاتصال بـ MySQL
    echo "1. الاتصال بخادم MySQL...\n";
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✓ تم الاتصال بنجاح\n\n";

    // إنشاء قاعدة البيانات
    echo "2. إنشاء قاعدة البيانات...\n";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✓ تم إنشاء قاعدة البيانات: $database\n\n";

    // الاتصال بقاعدة البيانات
    echo "3. الاتصال بقاعدة البيانات...\n";
    $pdo->exec("USE `$database`");
    echo "✓ تم الاتصال بقاعدة البيانات\n\n";

    // قراءة وتنفيذ سكريبت SQL
    echo "4. تنفيذ سكريبت إنشاء الجداول...\n";
    
    if (!file_exists('database_setup.sql')) {
        throw new Exception("ملف database_setup.sql غير موجود");
    }
    
    $sql = file_get_contents('database_setup.sql');
    $statements = explode(';', $sql);
    
    $executedStatements = 0;
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement) && !preg_match('/^--/', $statement)) {
            try {
                $pdo->exec($statement);
                $executedStatements++;
            } catch (PDOException $e) {
                // تجاهل الأخطاء البسيطة مثل "table already exists"
                if (strpos($e->getMessage(), 'already exists') === false) {
                    echo "تحذير: " . $e->getMessage() . "\n";
                }
            }
        }
    }
    
    echo "✓ تم تنفيذ $executedStatements استعلام\n\n";

    // إنشاء ملف التكوين
    echo "5. إنشاء ملف التكوين...\n";
    
    $configContent = "<?php
/**
 * إعدادات قاعدة البيانات
 * تم إنشاؤه تلقائياً بواسطة سكريبت الإعداد
 */

class Database {
    private \$host = '$host';
    private \$db_name = '$database';
    private \$username = '$username';
    private \$password = '$password';
    private \$charset = 'utf8mb4';
    private \$conn;

    public function connect() {
        \$this->conn = null;
        
        try {
            \$dsn = \"mysql:host=\" . \$this->host . \";dbname=\" . \$this->db_name . \";charset=\" . \$this->charset;
            \$options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            
            \$this->conn = new PDO(\$dsn, \$this->username, \$this->password, \$options);
        } catch(PDOException \$e) {
            error_log(\"Connection Error: \" . \$e->getMessage());
            throw new Exception(\"فشل الاتصال بقاعدة البيانات\");
        }
        
        return \$this->conn;
    }

    public function disconnect() {
        \$this->conn = null;
    }

    public function getConnection() {
        if (\$this->conn === null) {
            return \$this->connect();
        }
        return \$this->conn;
    }

    public function query(\$sql, \$params = []) {
        try {
            \$stmt = \$this->getConnection()->prepare(\$sql);
            \$stmt->execute(\$params);
            return \$stmt;
        } catch(PDOException \$e) {
            error_log(\"Query Error: \" . \$e->getMessage());
            throw new Exception(\"خطأ في تنفيذ الاستعلام\");
        }
    }

    public function fetchOne(\$sql, \$params = []) {
        \$stmt = \$this->query(\$sql, \$params);
        return \$stmt->fetch();
    }

    public function fetchAll(\$sql, \$params = []) {
        \$stmt = \$this->query(\$sql, \$params);
        return \$stmt->fetchAll();
    }

    public function insert(\$sql, \$params = []) {
        \$this->query(\$sql, \$params);
        return \$this->getConnection()->lastInsertId();
    }

    public function update(\$sql, \$params = []) {
        \$stmt = \$this->query(\$sql, \$params);
        return \$stmt->rowCount();
    }

    public function delete(\$sql, \$params = []) {
        \$stmt = \$this->query(\$sql, \$params);
        return \$stmt->rowCount();
    }

    public function beginTransaction() {
        return \$this->getConnection()->beginTransaction();
    }

    public function commit() {
        return \$this->getConnection()->commit();
    }

    public function rollback() {
        return \$this->getConnection()->rollback();
    }
}

class DB {
    private static \$instance = null;

    public static function getInstance() {
        if (self::\$instance === null) {
            self::\$instance = new Database();
        }
        return self::\$instance;
    }

    public static function connection() {
        return self::getInstance()->getConnection();
    }

    public static function query(\$sql, \$params = []) {
        return self::getInstance()->query(\$sql, \$params);
    }

    public static function fetchOne(\$sql, \$params = []) {
        return self::getInstance()->fetchOne(\$sql, \$params);
    }

    public static function fetchAll(\$sql, \$params = []) {
        return self::getInstance()->fetchAll(\$sql, \$params);
    }

    public static function insert(\$sql, \$params = []) {
        return self::getInstance()->insert(\$sql, \$params);
    }

    public static function update(\$sql, \$params = []) {
        return self::getInstance()->update(\$sql, \$params);
    }

    public static function delete(\$sql, \$params = []) {
        return self::getInstance()->delete(\$sql, \$params);
    }
}
?>";

    // إنشاء مجلد config إذا لم يكن موجوداً
    if (!is_dir('config')) {
        mkdir('config', 0755, true);
        echo "✓ تم إنشاء مجلد config\n";
    }
    
    file_put_contents('config/database.php', $configContent);
    echo "✓ تم إنشاء ملف config/database.php\n\n";

    // إنشاء المجلدات المطلوبة
    echo "6. إنشاء المجلدات المطلوبة...\n";
    $directories = [
        'uploads',
        'uploads/products',
        'assets',
        'assets/images',
        'exports',
        'cache'
    ];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
            echo "✓ تم إنشاء مجلد: $dir\n";
        }
    }
    
    echo "\n";

    // التحقق من البيانات
    echo "7. التحقق من البيانات...\n";
    
    // التحقق من المستخدم الافتراضي
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE username = 'admin'");
    $stmt->execute();
    $result = $stmt->fetch();
    
    if ($result['count'] > 0) {
        echo "✓ تم العثور على المستخدم الافتراضي (admin)\n";
    } else {
        echo "⚠ لم يتم العثور على المستخدم الافتراضي\n";
    }
    
    // التحقق من الإعدادات
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM settings");
    $stmt->execute();
    $result = $stmt->fetch();
    
    echo "✓ تم العثور على {$result['count']} إعداد في النظام\n\n";

    // رسالة النجاح
    echo "🎉 تم إعداد النظام بنجاح!\n\n";
    echo "معلومات تسجيل الدخول الافتراضية:\n";
    echo "اسم المستخدم: admin\n";
    echo "كلمة المرور: admin123\n\n";
    echo "يمكنك الآن الوصول للنظام عبر: http://your-domain.com/login.php\n\n";
    echo "⚠ تذكر تغيير كلمة المرور الافتراضية فور تسجيل الدخول!\n";

} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
    echo "\nتفاصيل الخطأ:\n";
    echo $e->getTraceAsString() . "\n";
    exit(1);
}
?>
