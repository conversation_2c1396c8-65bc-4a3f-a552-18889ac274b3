<?php
/**
 * صفحة عرض تفاصيل عرض الأسعار
 * نظام إدارة عروض الأسعار الاحترافي
 */

require_once 'classes/Auth.php';
require_once 'classes/Quote.php';
require_once 'classes/Settings.php';

$auth = new Auth();
$auth->requireLogin();
$auth->requirePermission('view_quotes');

$user = $auth->getCurrentUser();
$quote = new Quote();
$settings = new Settings();

// الحصول على ID العرض
$quoteId = $_GET['id'] ?? 0;

if (!$quoteId) {
    header('Location: quotes.php?error=invalid_id');
    exit;
}

// الحصول على بيانات العرض
$quoteData = $quote->getById($quoteId);

if (!$quoteData) {
    header('Location: quotes.php?error=not_found');
    exit;
}

// معالجة تحديث الحالة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $newStatus = $_POST['status'];
    $result = $quote->updateStatus($quoteId, $newStatus);
    
    if ($result['success']) {
        $quoteData['status'] = $newStatus;
        $message = $result['message'];
        $messageType = 'success';
    } else {
        $message = $result['message'];
        $messageType = 'danger';
    }
}

$systemSettings = $settings->getSystemSettings();

// حالات العرض
$statusLabels = [
    'draft' => 'مسودة',
    'sent' => 'مرسل',
    'approved' => 'موافق عليه',
    'rejected' => 'مرفوض',
    'expired' => 'منتهي الصلاحية'
];

$statusColors = [
    'draft' => 'secondary',
    'sent' => 'primary',
    'approved' => 'success',
    'rejected' => 'danger',
    'expired' => 'warning'
];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض الأسعار <?php echo htmlspecialchars($quoteData['quote_number']); ?> - نظام إدارة عروض الأسعار</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        .quote-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }
        
        .quote-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .quote-status {
            font-size: 1.1rem;
        }
        
        .info-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 1.5rem;
        }
        
        .info-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 1rem;
            border-bottom: 2px solid var(--secondary-color);
            padding-bottom: 0.5rem;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.75rem;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #555;
        }
        
        .info-value {
            color: #333;
        }
        
        .items-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .totals-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 1.5rem;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.75rem;
            padding: 0.5rem 0;
        }
        
        .final-total {
            border-top: 2px solid var(--primary-color);
            padding-top: 1rem;
            margin-top: 1rem;
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .action-buttons {
            position: sticky;
            top: 20px;
            z-index: 100;
        }
        
        .notes-section {
            background: #fff3cd;
            border-right: 5px solid #ffc107;
            padding: 1.5rem;
            border-radius: 10px;
            margin-top: 1.5rem;
        }
        
        .terms-section {
            background: #d1ecf1;
            border-right: 5px solid #17a2b8;
            padding: 1.5rem;
            border-radius: 10px;
            margin-top: 1.5rem;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4>نظام العروض</h4>
        </div>
        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
            <li><a href="quotes.php" class="active"><i class="fas fa-file-invoice-dollar"></i> عروض الأسعار</a></li>
            <li><a href="customers.php"><i class="fas fa-users"></i> العملاء</a></li>
            <li><a href="products.php"><i class="fas fa-box"></i> المنتجات</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar"></i> التقارير</a></li>
            <?php if ($user['role'] === 'admin'): ?>
            <li><a href="users.php"><i class="fas fa-user-cog"></i> المستخدمين</a></li>
            <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
            <?php endif; ?>
            <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar">
            <div class="d-flex align-items-center">
                <a href="quotes.php" class="btn btn-outline-secondary me-3">
                    <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                </a>
                <h5 class="mb-0">عرض الأسعار <?php echo htmlspecialchars($quoteData['quote_number']); ?></h5>
            </div>
            <div class="d-flex align-items-center">
                <span class="me-3">مرحباً، <?php echo htmlspecialchars($user['full_name']); ?></span>
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>الملف الشخصي</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="container-fluid p-4">
            <?php if (isset($message)): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-8">
                    <!-- Quote Header -->
                    <div class="quote-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="quote-number"><?php echo htmlspecialchars($quoteData['quote_number']); ?></div>
                                <div class="quote-status">
                                    <span class="badge bg-<?php echo $statusColors[$quoteData['status']]; ?> fs-6">
                                        <?php echo $statusLabels[$quoteData['status']]; ?>
                                    </span>
                                </div>
                            </div>
                            <div class="text-end">
                                <div>تاريخ العرض: <?php echo date('Y/m/d', strtotime($quoteData['quote_date'])); ?></div>
                                <div>صالح حتى: <?php echo date('Y/m/d', strtotime($quoteData['valid_until'])); ?></div>
                            </div>
                        </div>
                    </div>

                    <!-- Customer Info -->
                    <div class="info-card">
                        <div class="info-title">
                            <i class="fas fa-user me-2"></i>بيانات العميل
                        </div>
                        <div class="info-row">
                            <span class="info-label">الاسم:</span>
                            <span class="info-value"><?php echo htmlspecialchars($quoteData['customer_name']); ?></span>
                        </div>
                        <?php if ($quoteData['customer_company']): ?>
                        <div class="info-row">
                            <span class="info-label">الشركة:</span>
                            <span class="info-value"><?php echo htmlspecialchars($quoteData['customer_company']); ?></span>
                        </div>
                        <?php endif; ?>
                        <?php if ($quoteData['customer_phone']): ?>
                        <div class="info-row">
                            <span class="info-label">الهاتف:</span>
                            <span class="info-value">
                                <a href="tel:<?php echo $quoteData['customer_phone']; ?>" class="text-decoration-none">
                                    <?php echo htmlspecialchars($quoteData['customer_phone']); ?>
                                </a>
                            </span>
                        </div>
                        <?php endif; ?>
                        <?php if ($quoteData['customer_email']): ?>
                        <div class="info-row">
                            <span class="info-label">البريد الإلكتروني:</span>
                            <span class="info-value">
                                <a href="mailto:<?php echo $quoteData['customer_email']; ?>" class="text-decoration-none">
                                    <?php echo htmlspecialchars($quoteData['customer_email']); ?>
                                </a>
                            </span>
                        </div>
                        <?php endif; ?>
                        <?php if ($quoteData['customer_address']): ?>
                        <div class="info-row">
                            <span class="info-label">العنوان:</span>
                            <span class="info-value"><?php echo htmlspecialchars($quoteData['customer_address']); ?></span>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Quote Items -->
                    <div class="info-card">
                        <div class="info-title">
                            <i class="fas fa-list me-2"></i>بنود العرض
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover items-table">
                                <thead class="table-dark">
                                    <tr>
                                        <th>م</th>
                                        <th>الوصف</th>
                                        <th>الكمية</th>
                                        <th>سعر الوحدة</th>
                                        <th>الإجمالي</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $itemNumber = 1; ?>
                                    <?php foreach ($quoteData['items'] as $item): ?>
                                    <tr>
                                        <td><?php echo $itemNumber++; ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($item['item_description']); ?></strong>
                                            <?php if ($item['product_name']): ?>
                                                <br><small class="text-muted">كود: <?php echo htmlspecialchars($item['part_number']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo number_format($item['quantity'], 2); ?></td>
                                        <td><?php echo number_format($item['unit_price'], 2); ?> <?php echo htmlspecialchars($systemSettings['currency_symbol']); ?></td>
                                        <td><strong><?php echo number_format($item['line_total'], 2); ?> <?php echo htmlspecialchars($systemSettings['currency_symbol']); ?></strong></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Totals -->
                    <div class="totals-card">
                        <div class="total-row">
                            <span class="info-label">المجموع الفرعي:</span>
                            <span class="info-value"><?php echo number_format($quoteData['subtotal'], 2); ?> <?php echo htmlspecialchars($systemSettings['currency_symbol']); ?></span>
                        </div>
                        
                        <?php if ($quoteData['discount_amount'] > 0): ?>
                        <div class="total-row">
                            <span class="info-label">الخصم (<?php echo number_format($quoteData['discount_rate'], 1); ?>%):</span>
                            <span class="info-value text-success">-<?php echo number_format($quoteData['discount_amount'], 2); ?> <?php echo htmlspecialchars($systemSettings['currency_symbol']); ?></span>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($quoteData['tax_amount'] > 0): ?>
                        <div class="total-row">
                            <span class="info-label">ضريبة القيمة المضافة (<?php echo number_format($quoteData['tax_rate'], 1); ?>%):</span>
                            <span class="info-value"><?php echo number_format($quoteData['tax_amount'], 2); ?> <?php echo htmlspecialchars($systemSettings['currency_symbol']); ?></span>
                        </div>
                        <?php endif; ?>
                        
                        <div class="total-row final-total">
                            <span>الإجمالي النهائي:</span>
                            <span><?php echo number_format($quoteData['total_amount'], 2); ?> <?php echo htmlspecialchars($systemSettings['currency_symbol']); ?></span>
                        </div>
                    </div>

                    <!-- Notes -->
                    <?php if ($quoteData['notes']): ?>
                    <div class="notes-section">
                        <h6 class="fw-bold text-warning-emphasis mb-3">
                            <i class="fas fa-sticky-note me-2"></i>ملاحظات
                        </h6>
                        <p class="mb-0"><?php echo nl2br(htmlspecialchars($quoteData['notes'])); ?></p>
                    </div>
                    <?php endif; ?>

                    <!-- Terms -->
                    <?php if ($quoteData['terms_conditions']): ?>
                    <div class="terms-section">
                        <h6 class="fw-bold text-info-emphasis mb-3">
                            <i class="fas fa-file-contract me-2"></i>الشروط والأحكام
                        </h6>
                        <p class="mb-0"><?php echo nl2br(htmlspecialchars($quoteData['terms_conditions'])); ?></p>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Action Sidebar -->
                <div class="col-lg-4">
                    <div class="action-buttons">
                        <!-- Actions Card -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <i class="fas fa-cogs me-2"></i>الإجراءات
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="quote_pdf.php?id=<?php echo $quoteData['id']; ?>" class="btn btn-danger" target="_blank">
                                        <i class="fas fa-file-pdf me-2"></i>تحميل PDF
                                    </a>
                                    
                                    <?php if ($auth->hasPermission('edit_quotes')): ?>
                                    <a href="quote_edit.php?id=<?php echo $quoteData['id']; ?>" class="btn btn-warning">
                                        <i class="fas fa-edit me-2"></i>تعديل العرض
                                    </a>
                                    <?php endif; ?>
                                    
                                    <button type="button" class="btn btn-success" onclick="sendQuote()">
                                        <i class="fas fa-envelope me-2"></i>إرسال بالبريد
                                    </button>
                                    
                                    <button type="button" class="btn btn-info" onclick="copyQuote()">
                                        <i class="fas fa-copy me-2"></i>نسخ العرض
                                    </button>
                                    
                                    <a href="quote_create.php?customer_id=<?php echo $quoteData['customer_id']; ?>" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>عرض جديد للعميل
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Status Update Card -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <i class="fas fa-exchange-alt me-2"></i>تحديث الحالة
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <div class="mb-3">
                                        <select name="status" class="form-select" required>
                                            <?php foreach ($statusLabels as $value => $label): ?>
                                            <option value="<?php echo $value; ?>" <?php echo $value === $quoteData['status'] ? 'selected' : ''; ?>>
                                                <?php echo $label; ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <button type="submit" name="update_status" class="btn btn-primary w-100">
                                        <i class="fas fa-save me-2"></i>تحديث الحالة
                                    </button>
                                </form>
                            </div>
                        </div>

                        <!-- Quote Info Card -->
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-info-circle me-2"></i>معلومات العرض
                            </div>
                            <div class="card-body">
                                <div class="info-row">
                                    <span class="info-label">تم الإنشاء:</span>
                                    <span class="info-value"><?php echo date('Y/m/d H:i', strtotime($quoteData['created_at'])); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">آخر تحديث:</span>
                                    <span class="info-value"><?php echo date('Y/m/d H:i', strtotime($quoteData['updated_at'])); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">أنشأ بواسطة:</span>
                                    <span class="info-value"><?php echo htmlspecialchars($quoteData['created_by_name']); ?></span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">عدد البنود:</span>
                                    <span class="info-value"><?php echo count($quoteData['items']); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function sendQuote() {
            // تنفيذ إرسال العرض بالبريد الإلكتروني
            alert('سيتم تنفيذ هذه الميزة قريباً');
        }
        
        function copyQuote() {
            if (confirm('هل تريد إنشاء نسخة من هذا العرض؟')) {
                window.location.href = 'quote_copy.php?id=<?php echo $quoteData['id']; ?>';
            }
        }
        
        // إخفاء رسائل التنبيه تلقائياً
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                alert.style.transition = 'opacity 0.5s ease';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }, 5000);
    </script>
</body>
</html>
