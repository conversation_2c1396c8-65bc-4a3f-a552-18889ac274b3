<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مرحباً بك - نظام إدارة عروض الأسعار الاحترافي</title>
    <style>
        * {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .welcome-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 800px;
            width: 100%;
            text-align: center;
        }
        
        .header {
            background: #2c3e50;
            color: white;
            padding: 3rem 2rem;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .body {
            padding: 3rem 2rem;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .feature {
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 10px;
            border-right: 5px solid #3498db;
        }
        
        .feature h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }
        
        .feature p {
            color: #666;
            line-height: 1.6;
        }
        
        .actions {
            margin-top: 3rem;
        }
        
        .btn {
            display: inline-block;
            padding: 1rem 2rem;
            margin: 0.5rem;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-success:hover {
            background: #229954;
            transform: translateY(-2px);
        }
        
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        
        .btn-warning:hover {
            background: #e67e22;
            transform: translateY(-2px);
        }
        
        .status {
            margin: 2rem 0;
            padding: 1rem;
            border-radius: 10px;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
            
            .btn {
                display: block;
                margin: 0.5rem 0;
            }
        }
    </style>
</head>
<body>
    <div class="welcome-container">
        <div class="header">
            <div class="icon">🏢</div>
            <h1>نظام إدارة عروض الأسعار الاحترافي</h1>
            <p>مرحباً بك في النظام الأكثر تطوراً لإدارة عروض الأسعار</p>
        </div>
        
        <div class="body">
            <?php
            // فحص حالة النظام
            $configExists = file_exists('config/database.php');
            $dbConnected = false;
            
            if ($configExists) {
                try {
                    require_once 'config/database.php';
                    $db = new Database();
                    $conn = $db->connect();
                    $dbConnected = true;
                } catch (Exception $e) {
                    $dbConnected = false;
                }
            }
            ?>
            
            <?php if ($configExists && $dbConnected): ?>
                <div class="status status-success">
                    <strong>✅ النظام جاهز للاستخدام!</strong><br>
                    تم إعداد قاعدة البيانات بنجاح ويمكنك الآن البدء في استخدام النظام.
                </div>
            <?php elseif ($configExists && !$dbConnected): ?>
                <div class="status status-warning">
                    <strong>⚠️ مشكلة في الاتصال بقاعدة البيانات</strong><br>
                    ملف التكوين موجود ولكن لا يمكن الاتصال بقاعدة البيانات. تحقق من الإعدادات.
                </div>
            <?php else: ?>
                <div class="status status-error">
                    <strong>❌ النظام غير مُعد بعد</strong><br>
                    يجب إعداد قاعدة البيانات أولاً قبل استخدام النظام.
                </div>
            <?php endif; ?>
            
            <div class="features">
                <div class="feature">
                    <h3>🧾 إدارة العملاء</h3>
                    <p>إضافة وتعديل وحذف العملاء مع بحث سريع وتتبع تاريخ التعامل</p>
                </div>
                
                <div class="feature">
                    <h3>💰 عروض الأسعار</h3>
                    <p>إنشاء عروض أسعار احترافية مع حساب تلقائي للضرائب والخصومات</p>
                </div>
                
                <div class="feature">
                    <h3>📦 إدارة المنتجات</h3>
                    <p>كتالوج شامل للمنتجات مع تصنيف وإدارة الأسعار والصور</p>
                </div>
                
                <div class="feature">
                    <h3>📄 توليد PDF</h3>
                    <p>تصميم عروض أسعار احترافية بصيغة PDF مع شعار الشركة</p>
                </div>
                
                <div class="feature">
                    <h3>👥 إدارة المستخدمين</h3>
                    <p>نظام صلاحيات متقدم مع تسجيل دخول آمن وتتبع الأنشطة</p>
                </div>
                
                <div class="feature">
                    <h3>📊 التقارير</h3>
                    <p>إحصائيات شاملة وتقارير مفصلة مع إمكانية التصدير</p>
                </div>
            </div>
            
            <div class="actions">
                <?php if ($configExists && $dbConnected): ?>
                    <a href="login.php" class="btn btn-primary">
                        🔐 تسجيل الدخول
                    </a>
                    <a href="dashboard.php" class="btn btn-success">
                        🏠 لوحة التحكم
                    </a>
                <?php else: ?>
                    <a href="quick_setup.php" class="btn btn-primary">
                        ⚡ إعداد سريع
                    </a>
                    <a href="install.php" class="btn btn-success">
                        🛠️ معالج التثبيت
                    </a>
                <?php endif; ?>
                
                <a href="debug.php" class="btn btn-warning">
                    🔍 تشخيص المشاكل
                </a>
            </div>
            
            <div style="margin-top: 3rem; padding-top: 2rem; border-top: 1px solid #eee; color: #666;">
                <h4>بيانات الدخول الافتراضية:</h4>
                <p><strong>اسم المستخدم:</strong> admin</p>
                <p><strong>كلمة المرور:</strong> admin123</p>
                <p style="margin-top: 1rem; font-size: 0.9rem;">
                    ⚠️ تذكر تغيير كلمة المرور فور تسجيل الدخول لأول مرة
                </p>
            </div>
        </div>
    </div>
</body>
</html>
