<?php
/**
 * صفحة إدارة المنتجات
 * نظام إدارة عروض الأسعار الاحترافي
 */

// التحقق من وجود ملف التكوين أولاً
if (!file_exists('config/database.php')) {
    header('Location: simple_setup.php');
    exit;
}

try {
    require_once 'classes/Auth.php';
    require_once 'classes/Product.php';

    $auth = new Auth();
    $auth->requireLogin();
    $auth->requirePermission('view_products');

    $user = $auth->getCurrentUser();
    $product = new Product();
} catch (Exception $e) {
    header('Location: simple_setup.php?error=classes_failed');
    exit;
}

// معالجة البحث والفلترة
$search = $_GET['search'] ?? '';
$category = $_GET['category'] ?? '';

$filters = [
    'search' => $search,
    'category' => $category
];

$products = $product->getAll($filters);

// الحصول على الفئات المتاحة
$categories = $product->getCategories();

// معالجة الحذف
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_product'])) {
    $productId = $_POST['product_id'];
    $result = $product->delete($productId);
    
    if ($result['success']) {
        header('Location: products.php?success=deleted');
        exit;
    } else {
        $error = $result['message'];
    }
}

$success = $_GET['success'] ?? '';
$error = $_GET['error'] ?? '';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنتجات - نظام إدارة عروض الأسعار</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        .product-card {
            transition: transform 0.2s, box-shadow 0.2s;
            height: 100%;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .product-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
        }
        
        .product-placeholder {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 3rem;
        }
        
        .price-tag {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .stock-badge {
            position: absolute;
            top: 10px;
            left: 10px;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .in-stock {
            background: #d4edda;
            color: #155724;
        }
        
        .low-stock {
            background: #fff3cd;
            color: #856404;
        }
        
        .out-of-stock {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4>نظام العروض</h4>
        </div>
        <ul class="sidebar-menu">
            <li><a href="simple_dashboard.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
            <li><a href="quotes.php"><i class="fas fa-file-invoice-dollar"></i> عروض الأسعار</a></li>
            <li><a href="customers.php"><i class="fas fa-users"></i> العملاء</a></li>
            <li><a href="products.php" class="active"><i class="fas fa-box"></i> المنتجات</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar"></i> التقارير</a></li>
            <?php if ($user['role'] === 'admin'): ?>
            <li><a href="users.php"><i class="fas fa-user-cog"></i> المستخدمين</a></li>
            <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
            <?php endif; ?>
            <li><a href="simple_logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar">
            <div class="d-flex align-items-center">
                <h5 class="mb-0">إدارة المنتجات</h5>
            </div>
            <div class="d-flex align-items-center">
                <span class="me-3">مرحباً، <?php echo htmlspecialchars($user['full_name']); ?></span>
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>الملف الشخصي</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="simple_logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="container-fluid p-4">
            <!-- رسائل النجاح والخطأ -->
            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php
                    switch ($success) {
                        case 'created': echo 'تم إضافة المنتج بنجاح'; break;
                        case 'updated': echo 'تم تحديث المنتج بنجاح'; break;
                        case 'deleted': echo 'تم حذف المنتج بنجاح'; break;
                    }
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- أزرار الإجراءات والفلترة -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <a href="product_create.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة منتج جديد
                    </a>
                    
                    <?php if ($user['role'] === 'admin'): ?>
                    <a href="products_export.php" class="btn btn-outline-success">
                        <i class="fas fa-download me-2"></i>تصدير
                    </a>
                    
                    <a href="products_import.php" class="btn btn-outline-info">
                        <i class="fas fa-upload me-2"></i>استيراد
                    </a>
                    <?php endif; ?>
                </div>
                
                <div class="col-md-6">
                    <form method="GET" class="row g-2">
                        <div class="col-md-6">
                            <input type="text" name="search" class="form-control" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="البحث عن منتج...">
                        </div>
                        
                        <div class="col-md-4">
                            <select name="category" class="form-select">
                                <option value="">جميع الفئات</option>
                                <?php foreach ($categories as $cat): ?>
                                <option value="<?php echo htmlspecialchars($cat); ?>" 
                                        <?php echo $category === $cat ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($cat); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-box fa-2x text-primary mb-2"></i>
                            <h5><?php echo count($products); ?></h5>
                            <small class="text-muted">إجمالي المنتجات</small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-layer-group fa-2x text-success mb-2"></i>
                            <h5><?php echo count($categories); ?></h5>
                            <small class="text-muted">الفئات</small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <h5><?php echo count(array_filter($products, function($p) { return $p['stock_quantity'] > 10; })); ?></h5>
                            <small class="text-muted">متوفر</small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                            <h5><?php echo count(array_filter($products, function($p) { return $p['stock_quantity'] <= 5; })); ?></h5>
                            <small class="text-muted">مخزون منخفض</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قائمة المنتجات -->
            <?php if (empty($products)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-box fa-5x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد منتجات</h4>
                    <p class="text-muted">ابدأ بإضافة منتج جديد</p>
                    <a href="product_create.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة منتج
                    </a>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($products as $prod): ?>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card product-card">
                            <div class="card-body">
                                <div class="position-relative mb-3">
                                    <?php if ($prod['image_url']): ?>
                                        <img src="<?php echo htmlspecialchars($prod['image_url']); ?>" 
                                             alt="<?php echo htmlspecialchars($prod['product_name']); ?>" 
                                             class="product-image">
                                    <?php else: ?>
                                        <div class="product-placeholder">
                                            <i class="fas fa-image"></i>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <!-- شارة المخزون -->
                                    <?php
                                    $stockClass = 'in-stock';
                                    $stockText = 'متوفر';
                                    if ($prod['stock_quantity'] <= 0) {
                                        $stockClass = 'out-of-stock';
                                        $stockText = 'نفد المخزون';
                                    } elseif ($prod['stock_quantity'] <= 5) {
                                        $stockClass = 'low-stock';
                                        $stockText = 'مخزون منخفض';
                                    }
                                    ?>
                                    <span class="stock-badge <?php echo $stockClass; ?>">
                                        <?php echo $stockText; ?>
                                    </span>
                                </div>
                                
                                <h6 class="card-title"><?php echo htmlspecialchars($prod['product_name']); ?></h6>
                                
                                <p class="text-muted small mb-2">
                                    <strong>رقم القطعة:</strong> <?php echo htmlspecialchars($prod['part_number']); ?>
                                </p>
                                
                                <?php if ($prod['category']): ?>
                                <p class="text-muted small mb-2">
                                    <i class="fas fa-tag me-1"></i><?php echo htmlspecialchars($prod['category']); ?>
                                </p>
                                <?php endif; ?>
                                
                                <p class="text-muted small mb-3">
                                    <strong>المخزون:</strong> <?php echo $prod['stock_quantity']; ?> قطعة
                                </p>
                                
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span class="price-tag">
                                        <?php echo number_format($prod['unit_price'], 2); ?> ر.س
                                    </span>
                                </div>
                                
                                <div class="btn-group w-100" role="group">
                                    <a href="product_view.php?id=<?php echo $prod['id']; ?>" 
                                       class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                    
                                    <a href="product_edit.php?id=<?php echo $prod['id']; ?>" 
                                       class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-edit"></i> تعديل
                                    </a>
                                    
                                    <button type="button" class="btn btn-outline-danger btn-sm" 
                                            onclick="confirmDelete(<?php echo $prod['id']; ?>, '<?php echo htmlspecialchars($prod['product_name']); ?>')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Modal تأكيد الحذف -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف المنتج <strong id="deleteProductName"></strong>؟</p>
                    <p class="text-danger">لا يمكن التراجع عن هذا الإجراء.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="product_id" id="deleteProductId">
                        <button type="submit" name="delete_product" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function confirmDelete(productId, productName) {
            document.getElementById('deleteProductId').value = productId;
            document.getElementById('deleteProductName').textContent = productName;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
    </script>
</body>
</html>
