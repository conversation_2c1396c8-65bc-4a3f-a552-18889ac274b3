<?php
/**
 * كلاس إدارة عروض الأسعار
 * نظام إدارة عروض الأسعار الاحترافي
 */

class Quote {
    private $db;

    public function __construct() {
        $this->initDatabase();
    }

    /**
     * تهيئة قاعدة البيانات
     */
    private function initDatabase() {
        try {
            if (file_exists(__DIR__ . '/../config/database.php')) {
                require_once __DIR__ . '/../config/database.php';
                if (class_exists('DB')) {
                    $this->db = DB::getInstance();
                } elseif (class_exists('Database')) {
                    $this->db = new Database();
                } else {
                    throw new Exception('Database class not found');
                }
            } else {
                throw new Exception('Database config file not found');
            }
        } catch (Exception $e) {
            error_log("Database Init Error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * إنشاء عرض أسعار جديد
     */
    public function create($data) {
        try {
            $this->db->beginTransaction();
            
            // توليد رقم العرض
            $quoteNumber = $this->generateQuoteNumber();
            
            // إدراج العرض الأساسي
            $sql = "INSERT INTO quotes (quote_number, customer_id, quote_date, valid_until, status, 
                    subtotal, tax_rate, tax_amount, discount_rate, discount_amount, total_amount, 
                    notes, terms_conditions, created_by) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $quoteId = $this->db->insert($sql, [
                $quoteNumber,
                $data['customer_id'],
                $data['quote_date'],
                $data['valid_until'],
                $data['status'] ?? 'draft',
                $data['subtotal'] ?? 0,
                $data['tax_rate'] ?? 0,
                $data['tax_amount'] ?? 0,
                $data['discount_rate'] ?? 0,
                $data['discount_amount'] ?? 0,
                $data['total_amount'] ?? 0,
                $data['notes'] ?? null,
                $data['terms_conditions'] ?? null,
                $data['created_by']
            ]);
            
            // إدراج بنود العرض
            if (!empty($data['items'])) {
                $this->addItems($quoteId, $data['items']);
            }
            
            // إعادة حساب الإجماليات
            $this->recalculateTotal($quoteId);
            
            $this->db->commit();
            
            return [
                'success' => true,
                'message' => 'تم إنشاء عرض الأسعار بنجاح',
                'quote_id' => $quoteId,
                'quote_number' => $quoteNumber
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Create Quote Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء عرض الأسعار'
            ];
        }
    }

    /**
     * تحديث عرض الأسعار
     */
    public function update($id, $data) {
        try {
            $this->db->beginTransaction();
            
            // تحديث البيانات الأساسية
            $sql = "UPDATE quotes SET customer_id = ?, quote_date = ?, valid_until = ?, 
                    status = ?, notes = ?, terms_conditions = ? WHERE id = ?";
            
            $this->db->update($sql, [
                $data['customer_id'],
                $data['quote_date'],
                $data['valid_until'],
                $data['status'],
                $data['notes'] ?? null,
                $data['terms_conditions'] ?? null,
                $id
            ]);
            
            // تحديث البنود إذا تم تمريرها
            if (isset($data['items'])) {
                // حذف البنود القديمة
                $this->db->delete("DELETE FROM quote_items WHERE quote_id = ?", [$id]);
                
                // إضافة البنود الجديدة
                if (!empty($data['items'])) {
                    $this->addItems($id, $data['items']);
                }
            }
            
            // إعادة حساب الإجماليات
            $this->recalculateTotal($id);
            
            $this->db->commit();
            
            return [
                'success' => true,
                'message' => 'تم تحديث عرض الأسعار بنجاح'
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Update Quote Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث عرض الأسعار'
            ];
        }
    }

    /**
     * حذف عرض الأسعار
     */
    public function delete($id) {
        try {
            $this->db->beginTransaction();
            
            // حذف البنود أولاً
            $this->db->delete("DELETE FROM quote_items WHERE quote_id = ?", [$id]);
            
            // حذف العرض
            $affected = $this->db->delete("DELETE FROM quotes WHERE id = ?", [$id]);
            
            $this->db->commit();
            
            if ($affected > 0) {
                return [
                    'success' => true,
                    'message' => 'تم حذف عرض الأسعار بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'لم يتم العثور على عرض الأسعار'
                ];
            }
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Delete Quote Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف عرض الأسعار'
            ];
        }
    }

    /**
     * الحصول على عرض أسعار بواسطة ID
     */
    public function getById($id) {
        try {
            $sql = "SELECT q.*, c.name as customer_name, c.phone as customer_phone, 
                    c.email as customer_email, c.address as customer_address, 
                    c.company_name as customer_company, u.full_name as created_by_name 
                    FROM quotes q 
                    LEFT JOIN customers c ON q.customer_id = c.id 
                    LEFT JOIN users u ON q.created_by = u.id 
                    WHERE q.id = ?";
            
            $quote = $this->db->fetchOne($sql, [$id]);
            
            if ($quote) {
                // الحصول على البنود
                $quote['items'] = $this->getItems($id);
            }
            
            return $quote;
            
        } catch (Exception $e) {
            error_log("Get Quote Error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * الحصول على جميع عروض الأسعار
     */
    public function getAll($filters = []) {
        try {
            $sql = "SELECT q.*, c.name as customer_name, c.company_name as customer_company, 
                    u.full_name as created_by_name 
                    FROM quotes q 
                    LEFT JOIN customers c ON q.customer_id = c.id 
                    LEFT JOIN users u ON q.created_by = u.id 
                    WHERE 1=1";
            $params = [];
            
            // تطبيق الفلاتر
            if (!empty($filters['customer_id'])) {
                $sql .= " AND q.customer_id = ?";
                $params[] = $filters['customer_id'];
            }
            
            if (!empty($filters['status'])) {
                $sql .= " AND q.status = ?";
                $params[] = $filters['status'];
            }
            
            if (!empty($filters['date_from'])) {
                $sql .= " AND q.quote_date >= ?";
                $params[] = $filters['date_from'];
            }
            
            if (!empty($filters['date_to'])) {
                $sql .= " AND q.quote_date <= ?";
                $params[] = $filters['date_to'];
            }
            
            if (!empty($filters['search'])) {
                $sql .= " AND (q.quote_number LIKE ? OR c.name LIKE ? OR c.company_name LIKE ?)";
                $searchTerm = '%' . $filters['search'] . '%';
                $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
            }
            
            // ترتيب النتائج
            $orderBy = $filters['order_by'] ?? 'created_at';
            $orderDir = $filters['order_dir'] ?? 'DESC';
            $sql .= " ORDER BY q.$orderBy $orderDir";
            
            // تحديد عدد النتائج
            if (!empty($filters['limit'])) {
                $sql .= " LIMIT " . intval($filters['limit']);
                if (!empty($filters['offset'])) {
                    $sql .= " OFFSET " . intval($filters['offset']);
                }
            }
            
            return $this->db->fetchAll($sql, $params);
            
        } catch (Exception $e) {
            error_log("Get All Quotes Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * الحصول على بنود عرض الأسعار
     */
    public function getItems($quoteId) {
        try {
            $sql = "SELECT qi.*, p.product_name, p.part_number 
                    FROM quote_items qi 
                    LEFT JOIN products p ON qi.product_id = p.id 
                    WHERE qi.quote_id = ? 
                    ORDER BY qi.sort_order, qi.id";
            
            return $this->db->fetchAll($sql, [$quoteId]);
            
        } catch (Exception $e) {
            error_log("Get Quote Items Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * إضافة بنود إلى عرض الأسعار
     */
    private function addItems($quoteId, $items) {
        $sql = "INSERT INTO quote_items (quote_id, product_id, item_description, quantity, unit_price, line_total, sort_order) 
                VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        foreach ($items as $index => $item) {
            $lineTotal = $item['quantity'] * $item['unit_price'];
            
            $this->db->insert($sql, [
                $quoteId,
                $item['product_id'] ?? null,
                $item['item_description'],
                $item['quantity'],
                $item['unit_price'],
                $lineTotal,
                $index + 1
            ]);
        }
    }

    /**
     * إعادة حساب إجماليات العرض
     */
    public function recalculateTotal($quoteId) {
        try {
            // حساب المجموع الفرعي
            $result = $this->db->fetchOne(
                "SELECT SUM(line_total) as subtotal FROM quote_items WHERE quote_id = ?",
                [$quoteId]
            );
            $subtotal = $result['subtotal'] ?? 0;
            
            // الحصول على نسب الضريبة والخصم
            $quote = $this->db->fetchOne(
                "SELECT tax_rate, discount_rate FROM quotes WHERE id = ?",
                [$quoteId]
            );
            
            $taxRate = $quote['tax_rate'] ?? 0;
            $discountRate = $quote['discount_rate'] ?? 0;
            
            // حساب الخصم
            $discountAmount = ($subtotal * $discountRate) / 100;
            $afterDiscount = $subtotal - $discountAmount;
            
            // حساب الضريبة
            $taxAmount = ($afterDiscount * $taxRate) / 100;
            
            // الإجمالي النهائي
            $totalAmount = $afterDiscount + $taxAmount;
            
            // تحديث العرض
            $this->db->update(
                "UPDATE quotes SET subtotal = ?, tax_amount = ?, discount_amount = ?, total_amount = ? WHERE id = ?",
                [$subtotal, $taxAmount, $discountAmount, $totalAmount, $quoteId]
            );
            
            return [
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'discount_amount' => $discountAmount,
                'total_amount' => $totalAmount
            ];
            
        } catch (Exception $e) {
            error_log("Recalculate Total Error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * توليد رقم عرض أسعار جديد
     */
    private function generateQuoteNumber() {
        $year = date('Y');
        $month = date('m');
        
        // البحث عن آخر رقم في الشهر الحالي
        $sql = "SELECT quote_number FROM quotes 
                WHERE quote_number LIKE ? 
                ORDER BY id DESC LIMIT 1";
        
        $pattern = "QT-$year-$month-%";
        $lastQuote = $this->db->fetchOne($sql, [$pattern]);
        
        if ($lastQuote) {
            // استخراج الرقم التسلسلي
            $parts = explode('-', $lastQuote['quote_number']);
            $sequence = intval(end($parts)) + 1;
        } else {
            $sequence = 1;
        }
        
        return sprintf("QT-%s-%s-%04d", $year, $month, $sequence);
    }

    /**
     * تغيير حالة العرض
     */
    public function updateStatus($id, $status) {
        try {
            $validStatuses = ['draft', 'sent', 'approved', 'rejected', 'expired'];
            
            if (!in_array($status, $validStatuses)) {
                return [
                    'success' => false,
                    'message' => 'حالة غير صحيحة'
                ];
            }
            
            $affected = $this->db->update(
                "UPDATE quotes SET status = ? WHERE id = ?",
                [$status, $id]
            );
            
            if ($affected > 0) {
                return [
                    'success' => true,
                    'message' => 'تم تحديث حالة العرض بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'لم يتم العثور على العرض'
                ];
            }
            
        } catch (Exception $e) {
            error_log("Update Quote Status Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث حالة العرض'
            ];
        }
    }
}
