<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة عروض الأسعار</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f8f9fa;
            direction: rtl;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        .welcome {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        .stat-label {
            color: #666;
            font-size: 1.1rem;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .feature-card h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 1rem;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn-danger {
            background: #e74c3c;
        }
        .btn-danger:hover {
            background: #c0392b;
        }
        .user-info {
            color: white;
        }
        .logout {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            background: #e74c3c;
            border-radius: 5px;
        }
        .logout:hover {
            background: #c0392b;
        }
    </style>
</head>
<body>
    <?php
    session_start();
    
    // التحقق من تسجيل الدخول
    if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
        header('Location: simple_login.php');
        exit;
    }
    
    $user = [
        'full_name' => $_SESSION['full_name'] ?? 'المستخدم',
        'role' => $_SESSION['role'] ?? 'employee'
    ];
    
    // إحصائيات وهمية للعرض
    $stats = [
        'total_quotes' => 15,
        'pending_quotes' => 5,
        'customers' => 8,
        'products' => 25
    ];
    ?>
    
    <div class="header">
        <div>
            <h1>🏢 نظام إدارة عروض الأسعار</h1>
        </div>
        <div class="user-info">
            مرحباً، <?php echo htmlspecialchars($user['full_name']); ?>
            <a href="simple_logout.php" class="logout">تسجيل الخروج</a>
        </div>
    </div>
    
    <div class="container">
        <div class="welcome">
            <h2>🎉 مرحباً بك في لوحة التحكم</h2>
            <p>تم تسجيل دخولك بنجاح! النظام جاهز للاستخدام.</p>
            <p><strong>دورك:</strong> <?php echo $user['role'] === 'admin' ? 'مدير النظام' : 'موظف'; ?></p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-icon">📄</div>
                <div class="stat-number"><?php echo $stats['total_quotes']; ?></div>
                <div class="stat-label">إجمالي العروض</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">⏳</div>
                <div class="stat-number"><?php echo $stats['pending_quotes']; ?></div>
                <div class="stat-label">عروض معلقة</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-number"><?php echo $stats['customers']; ?></div>
                <div class="stat-label">العملاء</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">📦</div>
                <div class="stat-number"><?php echo $stats['products']; ?></div>
                <div class="stat-label">المنتجات</div>
            </div>
        </div>
        
        <div class="features">
            <div class="feature-card">
                <h3>📋 إدارة العملاء</h3>
                <p>إضافة وتعديل وحذف العملاء مع بحث سريع وتتبع تاريخ التعامل.</p>
                <a href="customers.php" class="btn">إدارة العملاء</a>
                <a href="customer_create.php" class="btn btn-sm" style="background: #28a745; margin-top: 10px;">إضافة عميل جديد</a>
            </div>

            <div class="feature-card">
                <h3>💰 عروض الأسعار</h3>
                <p>إنشاء عروض أسعار احترافية مع حساب تلقائي للضرائب والخصومات.</p>
                <a href="quotes.php" class="btn">عروض الأسعار</a>
                <a href="quote_create.php" class="btn btn-sm" style="background: #28a745; margin-top: 10px;">إنشاء عرض جديد</a>
            </div>

            <div class="feature-card">
                <h3>📦 إدارة المنتجات</h3>
                <p>كتالوج شامل للمنتجات مع تصنيف وإدارة الأسعار والصور.</p>
                <a href="products.php" class="btn">إدارة المنتجات</a>
                <a href="product_create.php" class="btn btn-sm" style="background: #28a745; margin-top: 10px;">إضافة منتج جديد</a>
            </div>
            
            <div class="feature-card">
                <h3>📊 التقارير</h3>
                <p>إحصائيات شاملة وتقارير مفصلة مع إمكانية التصدير.</p>
                <a href="reports.php" class="btn">التقارير</a>
            </div>
            
            <?php if ($user['role'] === 'admin'): ?>
            <div class="feature-card">
                <h3>⚙️ إعدادات النظام</h3>
                <p>إعدادات الشركة والضرائب والعملة وتخصيص النظام.</p>
                <a href="settings.php" class="btn">الإعدادات</a>
            </div>
            
            <div class="feature-card">
                <h3>👥 إدارة المستخدمين</h3>
                <p>إضافة وإدارة المستخدمين وتحديد الصلاحيات.</p>
                <a href="users.php" class="btn">المستخدمين</a>
            </div>
            <?php endif; ?>
        </div>
        
        <div style="margin-top: 3rem; padding: 2rem; background: white; border-radius: 10px; text-align: center;">
            <h3>🚀 النظام جاهز للاستخدام!</h3>
            <p>تم إعداد النظام بنجاح ويمكنك الآن البدء في استخدام جميع الميزات.</p>
            <p>للحصول على المساعدة، راجع ملف <strong>README.md</strong> أو <strong>QUICK_START.md</strong></p>
            
            <div style="margin-top: 2rem;">
                <a href="welcome.php" class="btn">🏠 صفحة الترحيب</a>
                <a href="start.html" class="btn">📋 صفحة البداية</a>
                <a href="debug.php" class="btn">🔍 تشخيص النظام</a>
            </div>
        </div>
    </div>
</body>
</html>
