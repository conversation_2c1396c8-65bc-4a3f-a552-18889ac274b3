document.getElementById('partNumber').addEventListener('input', function() {
    const partNumber = this.value;

    if (partNumber.length > 0) {
        fetch('getProduct.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `partNumber=${partNumber}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert(data.error);
            } else {
                document.getElementById('productName').textContent = data.product_name;
                document.getElementById('productImage').src = data.product_image;
                document.getElementById('unitPrice').textContent = data.unit_price;
                document.getElementById('unitCost').textContent = data.unit_cost;
                document.getElementById('productInfo').style.display = 'block';
            }
        })
        .catch(error => console.error('Error:', error));
    } else {
        document.getElementById('productInfo').style.display = 'none';
    }
});