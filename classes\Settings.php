<?php
/**
 * كلاس إدارة الإعدادات
 * نظام إدارة عروض الأسعار الاحترافي
 */

class Settings {
    private $db;
    private $settings = [];

    public function __construct() {
        $this->initDatabase();
        $this->loadSettings();
    }

    /**
     * تهيئة قاعدة البيانات
     */
    private function initDatabase() {
        try {
            if (file_exists(__DIR__ . '/../config/database.php')) {
                require_once __DIR__ . '/../config/database.php';
                if (class_exists('DB')) {
                    $this->db = DB::getInstance();
                } elseif (class_exists('Database')) {
                    $this->db = new Database();
                } else {
                    throw new Exception('Database class not found');
                }
            } else {
                throw new Exception('Database config file not found');
            }
        } catch (Exception $e) {
            error_log("Database Init Error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * تحميل جميع الإعدادات
     */
    private function loadSettings() {
        try {
            $sql = "SELECT setting_key, setting_value, setting_type FROM settings";
            $result = $this->db->fetchAll($sql);
            
            foreach ($result as $setting) {
                $this->settings[$setting['setting_key']] = $this->convertValue(
                    $setting['setting_value'], 
                    $setting['setting_type']
                );
            }
            
        } catch (Exception $e) {
            error_log("Load Settings Error: " . $e->getMessage());
        }
    }

    /**
     * تحويل القيمة حسب النوع
     */
    private function convertValue($value, $type) {
        switch ($type) {
            case 'number':
                return is_numeric($value) ? (float)$value : 0;
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'json':
                return json_decode($value, true) ?? [];
            default:
                return $value;
        }
    }

    /**
     * الحصول على قيمة إعداد
     */
    public function get($key, $default = null) {
        return $this->settings[$key] ?? $default;
    }

    /**
     * تحديث قيمة إعداد
     */
    public function set($key, $value, $type = 'text') {
        try {
            // تحويل القيمة للتخزين
            $storageValue = $this->prepareForStorage($value, $type);
            
            // التحقق من وجود الإعداد
            $existing = $this->db->fetchOne(
                "SELECT id FROM settings WHERE setting_key = ?",
                [$key]
            );
            
            if ($existing) {
                // تحديث الإعداد الموجود
                $this->db->update(
                    "UPDATE settings SET setting_value = ?, setting_type = ? WHERE setting_key = ?",
                    [$storageValue, $type, $key]
                );
            } else {
                // إضافة إعداد جديد
                $this->db->insert(
                    "INSERT INTO settings (setting_key, setting_value, setting_type) VALUES (?, ?, ?)",
                    [$key, $storageValue, $type]
                );
            }
            
            // تحديث الكاش المحلي
            $this->settings[$key] = $this->convertValue($storageValue, $type);
            
            return [
                'success' => true,
                'message' => 'تم تحديث الإعداد بنجاح'
            ];
            
        } catch (Exception $e) {
            error_log("Set Setting Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث الإعداد'
            ];
        }
    }

    /**
     * تحضير القيمة للتخزين
     */
    private function prepareForStorage($value, $type) {
        switch ($type) {
            case 'json':
                return json_encode($value);
            case 'boolean':
                return $value ? '1' : '0';
            default:
                return (string)$value;
        }
    }

    /**
     * تحديث عدة إعدادات
     */
    public function updateMultiple($settings) {
        try {
            $this->db->beginTransaction();
            
            $updated = 0;
            $errors = [];
            
            foreach ($settings as $key => $data) {
                $value = $data['value'] ?? '';
                $type = $data['type'] ?? 'text';
                
                $result = $this->set($key, $value, $type);
                if ($result['success']) {
                    $updated++;
                } else {
                    $errors[] = "خطأ في تحديث $key: " . $result['message'];
                }
            }
            
            if (empty($errors)) {
                $this->db->commit();
                return [
                    'success' => true,
                    'message' => "تم تحديث $updated إعداد بنجاح"
                ];
            } else {
                $this->db->rollback();
                return [
                    'success' => false,
                    'message' => implode('<br>', $errors)
                ];
            }
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("Update Multiple Settings Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث الإعدادات'
            ];
        }
    }

    /**
     * الحصول على جميع الإعدادات
     */
    public function getAll() {
        return $this->settings;
    }

    /**
     * الحصول على إعدادات الشركة
     */
    public function getCompanySettings() {
        return [
            'company_name' => $this->get('company_name', ''),
            'company_address' => $this->get('company_address', ''),
            'company_phone' => $this->get('company_phone', ''),
            'company_email' => $this->get('company_email', ''),
            'company_website' => $this->get('company_website', ''),
            'logo_path' => $this->get('logo_path', ''),
            'tax_number' => $this->get('tax_number', ''),
            'commercial_register' => $this->get('commercial_register', '')
        ];
    }

    /**
     * الحصول على إعدادات النظام
     */
    public function getSystemSettings() {
        return [
            'tax_rate' => $this->get('tax_rate', 15),
            'currency' => $this->get('currency', 'ريال سعودي'),
            'currency_symbol' => $this->get('currency_symbol', 'ر.س'),
            'quote_validity_days' => $this->get('quote_validity_days', 30),
            'decimal_places' => $this->get('decimal_places', 2),
            'date_format' => $this->get('date_format', 'Y/m/d'),
            'timezone' => $this->get('timezone', 'Asia/Riyadh')
        ];
    }

    /**
     * الحصول على إعدادات البريد الإلكتروني
     */
    public function getEmailSettings() {
        return [
            'smtp_host' => $this->get('smtp_host', ''),
            'smtp_port' => $this->get('smtp_port', 587),
            'smtp_username' => $this->get('smtp_username', ''),
            'smtp_password' => $this->get('smtp_password', ''),
            'smtp_encryption' => $this->get('smtp_encryption', 'tls'),
            'from_email' => $this->get('from_email', ''),
            'from_name' => $this->get('from_name', '')
        ];
    }

    /**
     * تحديث إعدادات الشركة
     */
    public function updateCompanySettings($data) {
        $settings = [
            'company_name' => ['value' => $data['company_name'], 'type' => 'text'],
            'company_address' => ['value' => $data['company_address'], 'type' => 'text'],
            'company_phone' => ['value' => $data['company_phone'], 'type' => 'text'],
            'company_email' => ['value' => $data['company_email'], 'type' => 'text'],
            'company_website' => ['value' => $data['company_website'], 'type' => 'text'],
            'tax_number' => ['value' => $data['tax_number'] ?? '', 'type' => 'text'],
            'commercial_register' => ['value' => $data['commercial_register'] ?? '', 'type' => 'text']
        ];
        
        return $this->updateMultiple($settings);
    }

    /**
     * تحديث إعدادات النظام
     */
    public function updateSystemSettings($data) {
        $settings = [
            'tax_rate' => ['value' => $data['tax_rate'], 'type' => 'number'],
            'currency' => ['value' => $data['currency'], 'type' => 'text'],
            'currency_symbol' => ['value' => $data['currency_symbol'], 'type' => 'text'],
            'quote_validity_days' => ['value' => $data['quote_validity_days'], 'type' => 'number'],
            'decimal_places' => ['value' => $data['decimal_places'], 'type' => 'number'],
            'date_format' => ['value' => $data['date_format'], 'type' => 'text'],
            'timezone' => ['value' => $data['timezone'], 'type' => 'text']
        ];
        
        return $this->updateMultiple($settings);
    }

    /**
     * تحديث إعدادات البريد الإلكتروني
     */
    public function updateEmailSettings($data) {
        $settings = [
            'smtp_host' => ['value' => $data['smtp_host'], 'type' => 'text'],
            'smtp_port' => ['value' => $data['smtp_port'], 'type' => 'number'],
            'smtp_username' => ['value' => $data['smtp_username'], 'type' => 'text'],
            'smtp_password' => ['value' => $data['smtp_password'], 'type' => 'text'],
            'smtp_encryption' => ['value' => $data['smtp_encryption'], 'type' => 'text'],
            'from_email' => ['value' => $data['from_email'], 'type' => 'text'],
            'from_name' => ['value' => $data['from_name'], 'type' => 'text']
        ];
        
        return $this->updateMultiple($settings);
    }

    /**
     * رفع شعار الشركة
     */
    public function uploadLogo($file) {
        try {
            $uploadDir = 'assets/images/';
            
            // إنشاء المجلد إذا لم يكن موجوداً
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }
            
            // التحقق من نوع الملف
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml'];
            if (!in_array($file['type'], $allowedTypes)) {
                return [
                    'success' => false,
                    'message' => 'نوع الملف غير مدعوم. يرجى رفع صورة بصيغة JPG, PNG, GIF أو SVG'
                ];
            }
            
            // التحقق من حجم الملف (2MB كحد أقصى)
            if ($file['size'] > 2 * 1024 * 1024) {
                return [
                    'success' => false,
                    'message' => 'حجم الملف كبير جداً. الحد الأقصى 2 ميجابايت'
                ];
            }
            
            // حذف الشعار القديم
            $oldLogo = $this->get('logo_path');
            if ($oldLogo && file_exists($oldLogo)) {
                unlink($oldLogo);
            }
            
            // إنشاء اسم للملف
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = 'logo.' . $extension;
            $filepath = $uploadDir . $filename;
            
            // رفع الملف
            if (move_uploaded_file($file['tmp_name'], $filepath)) {
                // تحديث مسار الشعار في الإعدادات
                $this->set('logo_path', $filepath);
                
                return [
                    'success' => true,
                    'filepath' => $filepath,
                    'message' => 'تم رفع الشعار بنجاح'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'فشل في رفع الملف'
                ];
            }
            
        } catch (Exception $e) {
            error_log("Upload Logo Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء رفع الشعار'
            ];
        }
    }

    /**
     * إعادة تعيين الإعدادات للقيم الافتراضية
     */
    public function resetToDefaults() {
        try {
            $defaultSettings = [
                'tax_rate' => ['value' => 15, 'type' => 'number'],
                'currency' => ['value' => 'ريال سعودي', 'type' => 'text'],
                'currency_symbol' => ['value' => 'ر.س', 'type' => 'text'],
                'quote_validity_days' => ['value' => 30, 'type' => 'number'],
                'decimal_places' => ['value' => 2, 'type' => 'number'],
                'date_format' => ['value' => 'Y/m/d', 'type' => 'text'],
                'timezone' => ['value' => 'Asia/Riyadh', 'type' => 'text']
            ];
            
            return $this->updateMultiple($defaultSettings);
            
        } catch (Exception $e) {
            error_log("Reset Settings Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء إعادة تعيين الإعدادات'
            ];
        }
    }
}
